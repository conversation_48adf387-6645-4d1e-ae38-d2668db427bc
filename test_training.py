#!/usr/bin/env python3

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import joblib
from collections import Counter
import logging
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_training():
    """Test training with dummy data"""
    data_path = "data/processed/dummy_cicids_data.csv"
    models_dir = "models/"
    
    logger.info(f"Loading data from {data_path}")
    
    try:
        df = pd.read_csv(data_path)
        logger.info(f"Data loaded successfully. Shape: {df.shape}")
        logger.info(f"Columns: {list(df.columns)}")
        logger.info(f"Label distribution: {Counter(df['Label'])}")
        
        # Separate features and target
        X = df.drop('Label', axis=1)
        y = df['Label']
        
        logger.info(f"Features shape: {X.shape}")
        logger.info(f"Target shape: {y.shape}")
        
        # Calculate anomaly percentage
        total_samples = len(df)
        anomaly_count = df[df['Label'] == 0].shape[0]
        anomaly_percentage = max(0.001, anomaly_count / total_samples)
        logger.info(f"Anomaly percentage: {anomaly_percentage:.4f}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        logger.info(f"Training set class distribution: {Counter(y_train)}")
        logger.info(f"Test set class distribution: {Counter(y_test)}")
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        logger.info("Features scaled successfully")
        
        # Train Random Forest
        logger.info("Training Random Forest...")
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced', n_jobs=-1)
        rf_model.fit(X_train, y_train)
        
        # Evaluate RF
        y_pred_rf = rf_model.predict(X_test)
        logger.info("Random Forest Evaluation:")
        logger.info(f"Confusion Matrix:\n{confusion_matrix(y_test, y_pred_rf)}")
        logger.info(f"Classification Report:\n{classification_report(y_test, y_pred_rf, target_names=['Anomaly (0)', 'Normal (1)'])}")
        
        # Train Isolation Forest
        logger.info("Training Isolation Forest...")
        iso_model = IsolationForest(random_state=42, contamination=anomaly_percentage, n_jobs=-1)
        
        # Train on normal data only (scaled)
        X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=X.columns)
        y_train_reset = y_train.reset_index(drop=True)
        normal_data = X_train_scaled_df[y_train_reset == 1]
        
        logger.info(f"Training Isolation Forest on {len(normal_data)} normal samples")
        iso_model.fit(normal_data)
        
        # Evaluate Isolation Forest
        X_test_scaled_df = pd.DataFrame(X_test_scaled, columns=X.columns)
        iso_pred_raw = iso_model.predict(X_test_scaled_df)
        iso_pred = np.where(iso_pred_raw == -1, 0, 1)  # Convert -1 to 0, 1 to 1
        
        logger.info("Isolation Forest Evaluation:")
        logger.info(f"Confusion Matrix:\n{confusion_matrix(y_test, iso_pred)}")
        logger.info(f"Classification Report:\n{classification_report(y_test, iso_pred, target_names=['Anomaly (0)', 'Normal (1)'])}")
        
        # Save models
        os.makedirs(models_dir, exist_ok=True)
        joblib.dump(rf_model, f"{models_dir}supervised_model.pkl")
        joblib.dump(iso_model, f"{models_dir}unsupervised_model.pkl")
        joblib.dump(scaler, f"{models_dir}scaler.pkl")
        
        logger.info("Models saved successfully!")
        
        # Test a few predictions
        logger.info("\nTesting predictions on a few samples:")
        for i in range(min(5, len(X_test))):
            sample = X_test.iloc[i:i+1]
            sample_scaled = scaler.transform(sample)
            
            rf_pred = rf_model.predict(sample)[0]
            rf_proba = rf_model.predict_proba(sample)[0]
            
            iso_pred_raw = iso_model.predict(sample_scaled)[0]
            iso_pred = 0 if iso_pred_raw == -1 else 1
            iso_score = iso_model.decision_function(sample_scaled)[0]
            
            actual = y_test.iloc[i]
            
            logger.info(f"Sample {i}: Actual={actual}, RF_pred={rf_pred} (proba={rf_proba}), ISO_pred={iso_pred} (score={iso_score:.3f})")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during training: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_training()
