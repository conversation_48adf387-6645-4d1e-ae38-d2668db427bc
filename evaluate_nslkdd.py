import pandas as pd
import numpy as np
import joblib # For loading models and scaler
import os
import logging
from collections import Counter
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, balanced_accuracy_score

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Model and file paths ---
MODELS_DIR = "models/"
# Corrected feature names file path
FEATURE_NAMES_PATH = os.path.join(MODELS_DIR, "selected_feature_names.pkl")
SCALER_PATH = os.path.join(MODELS_DIR, "scaler.pkl")
SUPERVISED_MODEL_PATH = os.path.join(MODELS_DIR, "supervised_model.pkl")
UNSUPERVISED_MODEL_PATH = os.path.join(MODELS_DIR, "unsupervised_model.pkl")

# Dummy data path (as we are no longer using NSL-KDD for evaluation)
DUMMY_DATA_PATH = "data/processed/dummy_cicids_data.csv"

def load_models_and_data():
    """
    Loads trained models, scaler, feature names, and the dummy test data.
    """
    scaler = None
    supervised_model = None
    unsupervised_model = None
    feature_names = []
    data = None

    try:
        # Load feature names (UPDATED PATH HERE)
        with open(FEATURE_NAMES_PATH, 'rb') as f:
            feature_names = joblib.load(f) # Use joblib.load for .pkl files
        logger.info(f"Features loaded from {FEATURE_NAMES_PATH}: {feature_names}")
        logger.info(f"Number of features: {len(feature_names)}")
    except FileNotFoundError:
        logger.error(f"Error: Feature names file not found at {FEATURE_NAMES_PATH}. Please run train_models.py first.")
        return None, None, None, None, None

    # Load dummy data
    logger.info(f"Loading dummy data from {DUMMY_DATA_PATH}...")
    try:
        data = pd.read_csv(DUMMY_DATA_PATH)
        logger.info(f"Dummy data loaded with shape: {data.shape}")
        logger.info(f"Columns in dummy data: {data.columns.tolist()}")
    except FileNotFoundError:
        logger.error(f"Error: Dummy data file not found at {DUMMY_DATA_PATH}. Please run generate_dummy_data.py first.")
        return None, None, None, None, None

    # Check if all required features exist in the dummy data
    missing_features = [f for f in feature_names if f not in data.columns]
    if missing_features:
        logger.error(f"Error: Dummy data is missing required features: {missing_features}. Please regenerate dummy data.")
        return None, None, None, None, None

    # Filter dummy data to include only the features the model was trained on
    # Ensure order is consistent
    X_eval = data[feature_names]
    y_true = data['Label']
    logger.info(f"Final feature set for evaluation: {X_eval.columns.tolist()}")

    # Load scaler
    try:
        scaler = joblib.load(SCALER_PATH)
        logger.info(f"Scaler loaded from {SCALER_PATH}.")
    except FileNotFoundError:
        logger.warning(f"Scaler file not found: {SCALER_PATH}. Skipping scaling for evaluation.")
        scaler = None # Proceed without scaler if not found, but it will affect results if models were scaled

    # Load supervised model
    try:
        supervised_model = joblib.load(SUPERVISED_MODEL_PATH)
        logger.info(f"Loading model: supervised_model.pkl...")
    except FileNotFoundError:
        logger.warning(f"Model file not found: {SUPERVISED_MODEL_PATH}. Skipping evaluation for this model.")

    # Load unsupervised model
    try:
        unsupervised_model = joblib.load(UNSUPERVISED_MODEL_PATH)
        logger.info(f"Loading model: unsupervised_model.pkl...")
    except FileNotFoundError:
        logger.warning(f"Model file not found: {UNSUPERVISED_MODEL_PATH}. Skipping evaluation for this model.")

    return scaler, supervised_model, unsupervised_model, X_eval, y_true, feature_names

def evaluate_models(scaler, supervised_model, unsupervised_model, X_eval, y_true):
    """
    Evaluates the loaded models on the provided data.
    """
    logger.info("\n--- Evaluation Summary ---")

    # Evaluate Supervised Model
    if supervised_model:
        logger.info("\n--- Supervised Model (RandomForest) Evaluation on Dummy Data ---")
        X_eval_sup = X_eval # RF was trained on unscaled data in train_models.py
        y_pred_sup = supervised_model.predict(X_eval_sup)
        
        logger.info(f"\nConfusion Matrix:\n{confusion_matrix(y_true, y_pred_sup)}")
        logger.info(f"\nClassification Report:\n{classification_report(y_true, y_pred_sup, target_names=['Anomaly (0)', 'Normal (1)'])}")
        logger.info(f"Accuracy: {accuracy_score(y_true, y_pred_sup):.4f}")
        logger.info(f"Balanced Accuracy: {balanced_accuracy_score(y_true, y_pred_sup):.4f}")
    else:
        logger.info("Supervised model not loaded. Skipping supervised evaluation.")

    # Evaluate Unsupervised Model
    if unsupervised_model:
        logger.info("\n--- Unsupervised Model (IsolationForest) Evaluation on Dummy Data ---")
        if scaler:
            X_eval_unsup_scaled = scaler.transform(X_eval)
        else:
            X_eval_unsup_scaled = X_eval # Use unscaled if scaler not available
            logger.warning("No scaler available for unsupervised model evaluation. Using unscaled data.")

        # Get anomaly scores
        anomaly_scores = unsupervised_model.decision_function(X_eval_unsup_scaled)

        # Predict based on anomaly scores (Isolation Forest predicts -1 for anomalies, 1 for normal)
        # Convert to 0 for anomaly, 1 for normal to match y_true
        y_pred_unsup = np.array([0 if score < 0 else 1 for score in anomaly_scores])
        
        logger.info(f"\nConfusion Matrix:\n{confusion_matrix(y_true, y_pred_unsup)}")
        logger.info(f"\nClassification Report:\n{classification_report(y_true, y_pred_unsup, target_names=['Anomaly (0)', 'Normal (1)'])}")
        logger.info(f"Accuracy: {accuracy_score(y_true, y_pred_unsup):.4f}")
        logger.info(f"Balanced Accuracy: {balanced_accuracy_score(y_true, y_pred_unsup):.4f}")

        logger.info(f"Max Anomaly Score (IF): {np.max(anomaly_scores):.4f}")
        logger.info(f"Min Anomaly Score (IF): {np.min(anomaly_scores):.4f}")
        logger.info(f"Mean Anomaly Score (IF): {np.mean(anomaly_scores):.4f}")

    else:
        logger.info("Unsupervised model not loaded. Skipping unsupervised evaluation.")

def main():
    scaler, supervised_model, unsupervised_model, X_eval, y_true, feature_names = load_models_and_data()

    if X_eval is not None and y_true is not None:
        evaluate_models(scaler, supervised_model, unsupervised_model, X_eval, y_true)
    else:
        logger.error("Could not load all necessary components for evaluation. Please check logs above.")

if __name__ == "__main__":
    main()

