# supervised_agent.py
import joblib
import logging
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class SupervisedAgent:
    def __init__(self, model_path="models/supervised_model.pkl"):
        """
        Loads the trained supervised model.
        """
        self.model = self._load_model(model_path)
        logger.info("SupervisedAgent initialized.")

    def _load_model(self, model_path):
        """Loads a pre-trained model."""
        try:
            model = joblib.load(model_path)
            logger.info(f"Supervised model loaded from {model_path}")
            return model
        except FileNotFoundError:
            logger.error(f"Supervised model not found at {model_path}. Please train it first.")
            return None # Or raise an error, depending on desired behavior

    def predict(self, data):
        """
        Makes predictions using the supervised model.
        Args:
            data (pd.DataFrame or np.array): Input features.
        Returns:
            np.array: Predicted labels (0 for anomaly, 1 for normal).
        """
        if self.model is None:
            logger.error("Model not loaded. Cannot predict.")
            return np.array([])
        if isinstance(data, np.ndarray):
            data = pd.DataFrame(data, columns=[f'feature_{i}' for i in range(data.shape[1])]) # Dummy column names for safety
        return self.model.predict(data)

    def predict_proba(self, data):
        """
        Gets prediction probabilities from the supervised model.
        Args:
            data (pd.DataFrame or np.array): Input features.
        Returns:
            np.array: Probabilities for each class [prob_class_0, prob_class_1].
        """
        if self.model is None:
            logger.error("Model not loaded. Cannot get probabilities.")
            return np.array([])
        if isinstance(data, np.ndarray):
            data = pd.DataFrame(data, columns=[f'feature_{i}' for i in range(data.shape[1])])
        return self.model.predict_proba(data)