# src/utils/xai_explainer.py
import shap
import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)

class XAI_Explainer:
    def __init__(self, model, feature_names):
        """
        Initializes the XAI Explainer.

        Args:
            model: The trained machine learning model (e.g., RandomForestClassifier).
                   It must have a .predict_proba() method if used for classification.
            feature_names (list): A list of feature names corresponding to the input data.
        """
        self.model = model
        self.feature_names = feature_names
        self.explainer = None
        self.background_data = None
        self.target_class_index = None 

        if not hasattr(self.model, 'predict_proba'):
            logger.warning("Provided model does not have a 'predict_proba' method. SHAP might not work as expected for classification.")
        logger.info("XAI_Explainer initialized (SHAP explainer not yet built).")

    def build_explainer(self, background_data, target_class_index):
        """
        Builds the SHAP explainer.

        Args:
            background_data (pd.DataFrame): Representative background data for SHAP.
                                            Should be a subset of your training data.
            target_class_index (int): The index of the class for which to explain predictions.
        """
        self.background_data = background_data
        self.target_class_index = target_class_index

        try:
            # For tree-based models, TreeExplainer is efficient
            self.explainer = shap.TreeExplainer(
                self.model,
                data=self.background_data,
                model_output="probability"
            )
            logger.info("SHAP TreeExplainer built successfully.")
        except Exception as e:
            logger.warning(f"Failed to build TreeExplainer: {e}. Attempting KernelExplainer (slower)...")
            try:
                def model_predict_proba_target(X):
                    X_df = pd.DataFrame(X, columns=self.feature_names)
                    return self.model.predict_proba(X_df)[:, self.target_class_index]

                self.explainer = shap.KernelExplainer(
                    model_predict_proba_target,
                    self.background_data
                )
                logger.info("SHAP KernelExplainer built successfully.")
            except Exception as ke:
                logger.error(f"Failed to build any SHAP explainer: {ke}", exc_info=True) # Added exc_info
                self.explainer = None


    def explain_instance(self, instance):
        """
        Generates SHAP explanations for a single data instance.

        Args:
            instance (pd.Series or np.array): A single data instance to explain.
                                             If np.array, assumes order matches feature_names.
        Returns:
            shap.Explanation object or None: The SHAP explanation object.
        """
        if self.explainer is None:
            logger.error("SHAP explainer not built. Cannot explain instance.")
            return None

        if isinstance(instance, pd.Series):
            instance_df = instance.to_frame().T
        elif isinstance(instance, np.ndarray):
            instance_df = pd.DataFrame([instance], columns=self.feature_names)
        else:
            logger.error(f"Unsupported instance type for explanation: {type(instance)}")
            return None

        try:
            shap_values = self.explainer.shap_values(instance_df)

            if isinstance(shap_values, list):
                if len(shap_values) > self.target_class_index:
                    shap_values_for_class = shap_values[self.target_class_index]
                else:
                    logger.error(f"SHAP values list has fewer elements than target_class_index ({self.target_class_index}).")
                    return None
            else:
                shap_values_for_class = shap_values 
            
            # NEW: Ensure shap_values_for_class is always a 1D numpy array for explanation.values
            final_shap_values = np.asarray(shap_values_for_class).flatten()

            explanation = shap.Explanation(
                values=final_shap_values, # Use the flattened array
                base_values=self.explainer.expected_value[self.target_class_index] if isinstance(self.explainer.expected_value, list) else self.explainer.expected_value,
                data=instance_df.iloc[0].values,
                feature_names=self.feature_names
            )
            return explanation
        except Exception as e:
            logger.error(f"Error during SHAP explanation for instance: {e}", exc_info=True)
            return None

    def get_top_features(self, explanation, num_features=5):
        """
        Extracts the top N important features from a SHAP explanation.

        Args:
            explanation (shap.Explanation): The SHAP explanation object.
            num_features (int): Number of top features to return.
        Returns:
            list: A list of tuples (feature_name, shap_value) for top features.
        """
        if explanation is None or explanation.values is None:
            logger.error("Explanation or its values are None, cannot get top features.")
            return []

        # Ensure values are a 1D numpy array for robust indexing
        shap_values_array = np.asarray(explanation.values).flatten()

        if len(shap_values_array) == 0:
            logger.warning("SHAP values array is empty, no features to extract.")
            return []

        # Get absolute SHAP values and sort by them
        abs_shap_values = np.abs(shap_values_array)
        sorted_indices = np.argsort(abs_shap_values)[::-1] # Descending order

        top_features_info = []
        for i in range(min(num_features, len(sorted_indices))):
            idx = int(sorted_indices[i]) # CRITICAL FIX: Explicitly cast to int to ensure scalar
            
            # Defensive check for index out of bounds, though it should not happen
            if idx < len(self.feature_names):
                feature_name = self.feature_names[idx]
                shap_value = shap_values_array[idx] 
                top_features_info.append((feature_name, shap_value))
            else:
                logger.warning(f"Index {idx} out of bounds for feature_names (len {len(self.feature_names)}). Skipping feature.")
        return top_features_info
