#!/usr/bin/env python3

print("=== Testing App Startup ===")

try:
    print("1. Testing basic imports...")
    import pandas as pd
    import numpy as np
    from flask import Flask
    print("   ✓ Basic imports successful")
    
    print("2. Testing decision agent import...")
    from decision_agent import DecisionAgent
    print("   ✓ Decision agent import successful")
    
    print("3. Testing data loading...")
    df = pd.read_csv("data/processed/dummy_cicids_data.csv", nrows=10)
    feature_names = df.drop('Label', axis=1).columns.tolist()
    print(f"   ✓ Data loaded with {len(feature_names)} features")
    
    print("4. Testing decision agent initialization...")
    decision_agent = DecisionAgent(
        feature_names=feature_names,
        supervised_model_path="models/supervised_model.pkl",
        unsupervised_model_path="models/unsupervised_model.pkl",
        scaler_path="models/scaler.pkl"
    )
    print("   ✓ Decision agent initialized")
    
    print("5. Testing Flask app creation...")
    app = Flask(__name__)
    print("   ✓ Flask app created")
    
    print("6. Testing a simple prediction...")
    test_sample = df.drop('Label', axis=1).iloc[0:1]
    result = decision_agent.make_decision(test_sample)
    print(f"   ✓ Prediction successful: {result['final_prediction']}")
    
    print("\n=== SUCCESS ===")
    print("All components are working. The app should be able to start.")
    print("Try running: python app.py")
    
except Exception as e:
    print(f"\n=== ERROR ===")
    print(f"✗ Error during startup test: {e}")
    import traceback
    traceback.print_exc()
