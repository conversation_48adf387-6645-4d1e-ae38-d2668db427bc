#!/usr/bin/env python3

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_plots_directory():
    """Create directory for saving plots"""
    plots_dir = "plots"
    os.makedirs(plots_dir, exist_ok=True)
    return plots_dir

def plot_confusion_matrices(X_test, y_test, rf_model, iso_model, scaler, plots_dir):
    """Plot confusion matrices for both models"""
    logger.info("Creating confusion matrices...")
    
    # Get predictions
    X_test_scaled = scaler.transform(X_test)
    rf_pred = rf_model.predict(X_test)
    iso_pred_raw = iso_model.predict(X_test_scaled)
    iso_pred = np.where(iso_pred_raw == -1, 0, 1)
    
    # Create confusion matrices
    cm_rf = confusion_matrix(y_test, rf_pred)
    cm_iso = confusion_matrix(y_test, iso_pred)
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Random Forest CM
    sns.heatmap(cm_rf, annot=True, fmt='d', cmap='Blues', ax=axes[0],
                xticklabels=['Anomaly', 'Normal'], yticklabels=['Anomaly', 'Normal'])
    axes[0].set_title('Random Forest Confusion Matrix')
    axes[0].set_xlabel('Predicted')
    axes[0].set_ylabel('Actual')
    
    # Isolation Forest CM
    sns.heatmap(cm_iso, annot=True, fmt='d', cmap='Oranges', ax=axes[1],
                xticklabels=['Anomaly', 'Normal'], yticklabels=['Anomaly', 'Normal'])
    axes[1].set_title('Isolation Forest Confusion Matrix')
    axes[1].set_xlabel('Predicted')
    axes[1].set_ylabel('Actual')
    
    plt.tight_layout()
    plt.savefig(f'{plots_dir}/confusion_matrices.png', dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Confusion matrices saved to {plots_dir}/confusion_matrices.png")

def plot_data_distribution(df, plots_dir):
    """Plot basic data distribution"""
    logger.info("Creating data distribution plot...")
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Label distribution pie chart
    label_counts = df['Label'].value_counts()
    axes[0].pie(label_counts.values, labels=['Anomaly (0)', 'Normal (1)'], autopct='%1.1f%%',
                colors=['#ff6b6b', '#4ecdc4'])
    axes[0].set_title('Label Distribution')
    
    # Feature value comparison for first feature
    normal_data = df[df['Label'] == 1].iloc[:, 0]  # First feature, normal data
    anomaly_data = df[df['Label'] == 0].iloc[:, 0]  # First feature, anomaly data
    
    axes[1].hist(normal_data, bins=30, alpha=0.7, label='Normal', color='#4ecdc4')
    axes[1].hist(anomaly_data, bins=30, alpha=0.7, label='Anomaly', color='#ff6b6b')
    axes[1].set_xlabel(df.columns[0])
    axes[1].set_ylabel('Frequency')
    axes[1].set_title(f'Distribution of {df.columns[0]}')
    axes[1].legend()
    
    plt.tight_layout()
    plt.savefig(f'{plots_dir}/data_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Data distribution saved to {plots_dir}/data_distribution.png")

def plot_feature_importance(rf_model, feature_names, plots_dir):
    """Plot top feature importance"""
    logger.info("Creating feature importance plot...")
    
    # Get feature importance
    importance = rf_model.feature_importances_
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False)
    
    # Plot top 15 features
    top_features = feature_importance_df.head(15)
    
    plt.figure(figsize=(10, 8))
    plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('Importance Score')
    plt.title('Top 15 Feature Importance (Random Forest)')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.savefig(f'{plots_dir}/feature_importance.png', dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Feature importance saved to {plots_dir}/feature_importance.png")
    
    return feature_importance_df

def plot_model_scores(X_test, y_test, rf_model, iso_model, scaler, plots_dir):
    """Plot model prediction scores"""
    logger.info("Creating model scores plot...")
    
    # Get predictions and scores
    X_test_scaled = scaler.transform(X_test)
    rf_proba = rf_model.predict_proba(X_test)[:, 0]  # Probability of anomaly
    iso_scores = iso_model.decision_function(X_test_scaled)
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Random Forest scores
    axes[0].hist(rf_proba[y_test == 1], bins=20, alpha=0.7, label='Normal', color='#4ecdc4')
    axes[0].hist(rf_proba[y_test == 0], bins=20, alpha=0.7, label='Anomaly', color='#ff6b6b')
    axes[0].set_xlabel('Anomaly Probability')
    axes[0].set_ylabel('Frequency')
    axes[0].set_title('Random Forest Scores')
    axes[0].legend()
    
    # Isolation Forest scores
    axes[1].hist(iso_scores[y_test == 1], bins=20, alpha=0.7, label='Normal', color='#4ecdc4')
    axes[1].hist(iso_scores[y_test == 0], bins=20, alpha=0.7, label='Anomaly', color='#ff6b6b')
    axes[1].set_xlabel('Anomaly Score')
    axes[1].set_ylabel('Frequency')
    axes[1].set_title('Isolation Forest Scores')
    axes[1].legend()
    
    plt.tight_layout()
    plt.savefig(f'{plots_dir}/model_scores.png', dpi=300, bbox_inches='tight')
    plt.close()
    logger.info(f"Model scores saved to {plots_dir}/model_scores.png")

def generate_performance_report(X_test, y_test, rf_model, iso_model, scaler, plots_dir):
    """Generate and save performance report"""
    logger.info("Generating performance report...")
    
    # Get predictions
    X_test_scaled = scaler.transform(X_test)
    rf_pred = rf_model.predict(X_test)
    iso_pred_raw = iso_model.predict(X_test_scaled)
    iso_pred = np.where(iso_pred_raw == -1, 0, 1)
    
    # Generate reports
    rf_report = classification_report(y_test, rf_pred, target_names=['Anomaly', 'Normal'])
    iso_report = classification_report(y_test, iso_pred, target_names=['Anomaly', 'Normal'])
    
    # Save to file
    with open(f'{plots_dir}/performance_report.txt', 'w') as f:
        f.write("ANMS Model Performance Report\n")
        f.write("=" * 50 + "\n\n")
        f.write("RANDOM FOREST CLASSIFIER\n")
        f.write("-" * 30 + "\n")
        f.write(rf_report)
        f.write("\n\n")
        f.write("ISOLATION FOREST\n")
        f.write("-" * 30 + "\n")
        f.write(iso_report)
        f.write("\n\n")
        f.write("DATA STATISTICS\n")
        f.write("-" * 30 + "\n")
        f.write(f"Total test samples: {len(y_test)}\n")
        f.write(f"Normal samples: {sum(y_test == 1)}\n")
        f.write(f"Anomaly samples: {sum(y_test == 0)}\n")
        f.write(f"Anomaly percentage: {sum(y_test == 0)/len(y_test)*100:.1f}%\n")
    
    logger.info(f"Performance report saved to {plots_dir}/performance_report.txt")

def main():
    """Main function to generate all plots"""
    logger.info("=== Generating Training Result Plots ===")
    
    try:
        # Create plots directory
        plots_dir = create_plots_directory()
        
        # Load data
        logger.info("Loading data...")
        df = pd.read_csv("data/processed/dummy_cicids_data.csv")
        X = df.drop('Label', axis=1)
        y = df['Label']
        feature_names = X.columns.tolist()
        
        # Load models
        logger.info("Loading trained models...")
        rf_model = joblib.load("models/supervised_model.pkl")
        iso_model = joblib.load("models/unsupervised_model.pkl")
        scaler = joblib.load("models/scaler.pkl")
        
        # Split data (same as training)
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Generate all plots
        plot_data_distribution(df, plots_dir)
        plot_confusion_matrices(X_test, y_test, rf_model, iso_model, scaler, plots_dir)
        feature_importance_df = plot_feature_importance(rf_model, feature_names, plots_dir)
        plot_model_scores(X_test, y_test, rf_model, iso_model, scaler, plots_dir)
        generate_performance_report(X_test, y_test, rf_model, iso_model, scaler, plots_dir)
        
        # Save feature importance to CSV
        feature_importance_df.to_csv(f'{plots_dir}/feature_importance.csv', index=False)
        
        logger.info("=== All plots generated successfully! ===")
        logger.info(f"Check the '{plots_dir}' directory for:")
        logger.info("  - data_distribution.png")
        logger.info("  - confusion_matrices.png") 
        logger.info("  - feature_importance.png")
        logger.info("  - model_scores.png")
        logger.info("  - performance_report.txt")
        logger.info("  - feature_importance.csv")
        
    except Exception as e:
        logger.error(f"Error generating plots: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
