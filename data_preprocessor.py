# data_preprocessor.py
import pandas as pd
import numpy as np
import os
import logging
from collections import Counter

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def preprocess_cicids2017(raw_data_dir="data/raw/", output_path="data/processed/cicids2017_processed.csv"):
    """
    Combines, cleans, and preprocesses the multiple CICIDS2017 CSV files into a single file.

    Args:
        raw_data_dir (str): Directory containing the raw CICIDS2017 CSV files.
                            e.g., 'data/raw/'.
        output_path (str): Path to save the combined and processed CSV file.
                           e.g., 'data/processed/cicids2017_processed.csv'.
    Returns:
        pd.DataFrame: The processed DataFrame.
    """
    all_files = [os.path.join(raw_data_dir, f) for f in os.listdir(raw_data_dir) if f.endswith('.csv')]
    if not all_files:
        logger.error(f"No CSV files found in {raw_data_dir}. Please place your CICIDS2017 CSVs there.")
        return None

    logger.info(f"Found {len(all_files)} CSV files in {raw_data_dir}.")

    list_df = []
    for f in all_files:
        logger.info(f"Reading {os.path.basename(f)}...")
        try:
            df = pd.read_csv(f)
            list_df.append(df)
        except Exception as e:
            logger.error(f"Error reading {os.path.basename(f)}: {e}")
            continue

    if not list_df:
        logger.error("No dataframes were successfully loaded. Exiting preprocessor.")
        return None

    logger.info("Concatenating all dataframes...")
    combined_df = pd.concat(list_df, ignore_index=True)
    logger.info(f"Combined DataFrame shape: {combined_df.shape}")

    # --- Data Cleaning and Preprocessing Steps ---
    logger.info("Starting data cleaning and preprocessing...")

    # 1. Clean column names (remove leading/trailing spaces)
    combined_df.columns = combined_df.columns.str.strip()
    logger.info("Column names stripped.")

    # 2. Handle duplicate columns (if any, e.g., 'Fwd PSH Flags.1')
    # This is a common issue with CICIDS2017
    # Get a list of column names
    cols = pd.Series(combined_df.columns)
    # Get columns that are duplicated
    for dup in cols[cols.duplicated()].unique():
        cols[cols[cols == dup].index.values.tolist()] = [dup + '.' + str(i) if i != 0 else dup for i in range(len(cols[cols == dup].index.values.tolist()))]
    combined_df.columns = cols
    logger.info("Handled potential duplicate column names.")


    # 3. Handle infinite values (common in CICIDS2017 due to division by zero or large numbers)
    # Replace infinities with NaN
    combined_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    logger.info("Replaced infinite values with NaN.")

    # 4. Handle NaN values (fill with 0 or mean/median, depending on feature)
    # For a quick prototype, we'll fill with 0, but mean/median is often better.
    combined_df.fillna(0, inplace=True)
    logger.info("Filled NaN values with 0.")

    # 5. Drop any remaining non-numeric columns that aren't the 'Label'
    numeric_cols = combined_df.select_dtypes(include=np.number).columns.tolist()
    if 'Label' in combined_df.columns and 'Label' not in numeric_cols:
        # If Label is not numeric, we'll convert it later
        pass
    else:
        # Drop columns that are not numeric and not 'Label'
        non_numeric_non_label_cols = [col for col in combined_df.columns if col not in numeric_cols and col != 'Label']
        if non_numeric_non_label_cols:
            combined_df.drop(columns=non_numeric_non_label_cols, inplace=True)
            logger.info(f"Dropped non-numeric, non-label columns: {non_numeric_non_label_cols}")


    # 6. Convert 'Label' column to numeric (0 for anomaly, 1 for normal)
    # CICIDS2017 has 'BENIGN' for normal traffic and various attack names for anomalies.
    if 'Label' in combined_df.columns:
        # Strip whitespace from labels first
        combined_df['Label'] = combined_df['Label'].str.strip()
        # Convert all attack labels to 'Anomaly' and 'BENIGN' to 'Normal'
        combined_df['Label'] = combined_df['Label'].apply(lambda x: 1 if x == 'BENIGN' else 0)
        logger.info(f"Converted labels: {Counter(combined_df['Label'])} (0=Anomaly, 1=Normal)")
    else:
        logger.error("'Label' column not found in the combined DataFrame. Please check your raw data files.")
        return None

    # Ensure all columns are numeric after preprocessing for model training
    for col in combined_df.columns:
        if combined_df[col].dtype == 'object' and col != 'Label':
            try:
                combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce')
                combined_df[col].fillna(0, inplace=True) # Fill NaNs from coercion
            except:
                logger.warning(f"Could not convert column '{col}' to numeric. Dropping it.")
                combined_df.drop(columns=[col], inplace=True)

    logger.info("Preprocessing complete.")
    logger.info(f"Final processed DataFrame shape: {combined_df.shape}")
    logger.info(f"Final processed DataFrame labels: {Counter(combined_df['Label'])}")

    # Save the processed DataFrame
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    combined_df.to_csv(output_path, index=False)
    logger.info(f"Processed data saved to {output_path}")

    return combined_df

if __name__ == "__main__":
    # Create directories if they don't exist
    if not os.path.exists("data/raw/"):
        os.makedirs("data/raw/")
    if not os.path.exists("data/processed/"):
        os.makedirs("data/processed/")

    # IMPORTANT: Place your raw CICIDS2017 daily CSV files into the 'data/raw/' folder
    # e.g., 'Monday-WorkingHours.pcap_ISCX.csv', 'Tuesday-WorkingHours.pcap_ISCX.csv', etc.

    processed_df = preprocess_cicids2017()
    if processed_df is not None:
        logger.info("Data preprocessing completed successfully.")
    else:
        logger.error("Data preprocessing failed.")