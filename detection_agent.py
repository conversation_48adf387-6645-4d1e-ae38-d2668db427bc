import os
import pickle
import pandas as pd
import numpy as np

print("--- Starting Network Detection Agent ---")

# --- Configuration (Must match train_models.py and data_preprocessor.py) ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
MODELS_DIR = os.path.join(BASE_DIR, 'models')

# Paths to saved artifacts
RF_MODEL_PATH = os.path.join(MODELS_DIR, 'rf_model.pkl')
ISO_MODEL_PATH = os.path.join(MODELS_DIR, 'iso_model.pkl')
SCALER_PATH = os.path.join(MODELS_DIR, 'scaler.pkl')
FEATURE_COLUMNS_PATH = os.path.join(MODELS_DIR, 'feature_columns.pkl')

# Target column name (must match data_preprocessor.py)
TARGET_COLUMN_NAME = 'Label' # The stripped version 'Label'

# --- 1. Load Saved Models and Artifacts ---
print("\nLoading saved models and artifacts...")
try:
    with open(RF_MODEL_PATH, 'rb') as f:
        rf_model = pickle.load(f)
    with open(ISO_MODEL_PATH, 'rb') as f:
        iso_model = pickle.load(f)
    with open(SCALER_PATH, 'rb') as f:
        scaler = pickle.load(f)
    with open(FEATURE_COLUMNS_PATH, 'rb') as f:
        feature_columns = pickle.load(f)

    print("Models and artifacts loaded successfully.")
    print(f"Loaded RF Model: {type(rf_model)}")
    print(f"Loaded Isolation Forest Model: {type(iso_model)}")
    print(f"Loaded Scaler: {type(scaler)}")
    print(f"Number of features expected by models: {len(feature_columns)}")
    print(f"Sample of feature columns: {feature_columns[:5]}...")


except FileNotFoundError as e:
    print(f"Error: One or more required model files not found. Please ensure you have run train_models.py successfully.")
    print(f"Missing file: {e.filename}")
    exit()
except Exception as e:
    print(f"An unexpected error occurred while loading models: {e}")
    exit()

# --- 2. Simulate New Network Traffic Data ---
# Number of flows to simulate
num_simulated_flows = 10

# Initialize a dictionary for new data with zeros for all features,
# but only for the features actually present in feature_columns
new_data_dict = {col: [0.0] * num_simulated_flows for col in feature_columns}

# Helper function to set a value only if the column exists
def set_if_exists(data_dict, col_name, row_idx, value):
    if col_name in data_dict:
        data_dict[col_name][row_idx] = value
    # else:
        # print(f"Warning: Column '{col_name}' not in loaded feature_columns. Skipping setting value for row {row_idx}.")
        # (Commented out warning for cleaner output, but useful for debugging)


# Create specific scenarios for demonstration
# Flow 0: Standard Benign Traffic (e.g., web Browse, light load)
set_if_exists(new_data_dict, 'Flow Duration', 0, 3000000) # 3 seconds
set_if_exists(new_data_dict, 'Total Fwd Packets', 0, 10)
set_if_exists(new_data_dict, 'Total Backward Packets', 0, 12)
set_if_exists(new_data_dict, 'Flow Bytes/s', 0, 50000.0)
set_if_exists(new_data_dict, 'Flow Packets/s', 0, 7.0)
set_if_exists(new_data_dict, 'Destination Port', 0, 80)
set_if_exists(new_data_dict, 'Subflow Fwd Bytes', 0, 1000)
set_if_exists(new_data_dict, 'Subflow Bwd Bytes', 0, 1500)
set_if_exists(new_data_dict, 'Packet Length Variance', 0, 100) # Benign low variance
set_if_exists(new_data_dict, 'Bwd Packet Length Mean', 0, 100)
set_if_exists(new_data_dict, 'Avg Bwd Segment Size', 0, 100)

# Flow 1: DoS/DDoS Attack Attempt (HIGH VOLUME, EXTREME RATES for important features)
set_if_exists(new_data_dict, 'Flow Duration', 1, 100000) # Short, intense burst
set_if_exists(new_data_dict, 'Total Fwd Packets', 1, 50000) # EXTREME: 50,000 packets
set_if_exists(new_data_dict, 'Total Backward Packets', 1, 0) # No response packets
set_if_exists(new_data_dict, 'Flow Bytes/s', 1, 1000000000.0) # EXTREME: 1 GB/s
set_if_exists(new_data_dict, 'Flow Packets/s', 1, 500000.0) # EXTREME: 500,000 packets/s
set_if_exists(new_data_dict, 'Destination Port', 1, 80) # Common web port
set_if_exists(new_data_dict, 'Packet Length Variance', 1, 1000000) # EXTREME: High variance
set_if_exists(new_data_dict, 'Bwd Packet Length Mean', 1, 0) # No backward packets
set_if_exists(new_data_dict, 'Avg Bwd Segment Size', 1, 0)
set_if_exists(new_data_dict, 'Packet Length Std', 1, 1000)
set_if_exists(new_data_dict, 'Average Packet Size', 1, 100)
set_if_exists(new_data_dict, 'Total Length of Fwd Packets', 1, 15000000) # EXTREME
set_if_exists(new_data_dict, 'Fwd IAT Std', 1, 1000) # May vary depending on attack
set_if_exists(new_data_dict, 'Bwd Packet Length Max', 1, 0)
set_if_exists(new_data_dict, 'Packet Length Mean', 1, 300)
set_if_exists(new_data_dict, 'Subflow Fwd Bytes', 1, 1000000)
set_if_exists(new_data_dict, 'Subflow Bwd Bytes', 1, 0)
set_if_exists(new_data_dict, 'Init_Win_bytes_forward', 1, 0) # Could be 0 for SYN flood

# Flow 2: Port Scan (Many short flows, high dest port variety - here, one distinct flow)
set_if_exists(new_data_dict, 'Flow Duration', 2, 100) # Very short duration
set_if_exists(new_data_dict, 'Total Fwd Packets', 2, 1) # Single packet probe
set_if_exists(new_data_dict, 'Total Backward Packets', 2, 0)
set_if_exists(new_data_dict, 'Destination Port', 2, 65000) # Unusual high port
set_if_exists(new_data_dict, 'Fwd Header Length', 2, 40)
set_if_exists(new_data_dict, 'Flow Bytes/s', 2, 1000) # Low rate
set_if_exists(new_data_dict, 'Flow Packets/s', 2, 10) # Low rate

# Flow 3: Heartbleed (Specific, low-volume, high byte/packet length variability)
set_if_exists(new_data_dict, 'Flow Duration', 3, 1000000) # 1 second
set_if_exists(new_data_dict, 'Total Fwd Packets', 3, 5)
set_if_exists(new_data_dict, 'Total Backward Packets', 3, 5)
set_if_exists(new_data_dict, 'Destination Port', 3, 443) # HTTPS port
set_if_exists(new_data_dict, 'Packet Length Mean', 3, 1000) # Larger than average packet
set_if_exists(new_data_dict, 'Packet Length Std', 3, 800) # High standard deviation in packet length
set_if_exists(new_data_dict, 'Fwd Header Length', 3, 200)
set_if_exists(new_data_dict, 'Flow Bytes/s', 3, 50000)
set_if_exists(new_data_dict, 'Flow Packets/s', 3, 10)

# Flows 4-9: Random noise, likely to be flagged by Isolation Forest
for i in range(4, num_simulated_flows):
    for col in feature_columns:
        if 'Port' in col or 'Flag' in col:
            set_if_exists(new_data_dict, col, i, np.random.choice([0, 80, 443, 22, 21, 23, np.random.randint(1024, 65535)]))
        else:
            set_if_exists(new_data_dict, col, i, np.random.rand() * 100000) # Scale values for demonstration

new_traffic_df = pd.DataFrame(new_data_dict)

print(f"\nSimulated {num_simulated_flows} new network flows:")
print(new_traffic_df.head())

# --- 3. Preprocess New Data ---
print("\nPreprocessing new data...")

# Drop columns not in feature_columns (this shouldn't happen now if new_data_dict is built carefully)
extra_cols = set(new_traffic_df.columns) - set(feature_columns)
if extra_cols:
    new_traffic_df.drop(columns=list(extra_cols), inplace=True)

# Reorder columns to match the training order. This is crucial for scaler/model.
new_traffic_df = new_traffic_df[feature_columns]

# Handle infinity values (same as during training for consistency)
inf_cols = ['Flow Bytes/s', 'Flow Packets/s']
for col in inf_cols:
    if col in new_traffic_df.columns:
        large_finite_value = 1e18 # Use a very large fixed number for safety
        new_traffic_df.replace([np.inf, -np.inf], large_finite_value, inplace=True)
    # else:
        # print(f"Warning: Infinity column '{col}' not found in new data. Skipping infinity handling for it.")

# Apply the loaded scaler to the new data
X_new_scaled = scaler.transform(new_traffic_df)
X_new_scaled_df = pd.DataFrame(X_new_scaled, columns=feature_columns, index=new_traffic_df.index)

print("New data preprocessed successfully.")
print(f"Preprocessed new data shape: {X_new_scaled_df.shape}")


# --- 4. Perform Dual-Layer Detection ---
print("\nPerforming dual-layer detection...")

# Supervised Detection (Random Forest Classifier)
print("--- Supervised Detection (Classification) ---")
supervised_predictions = rf_model.predict(X_new_scaled_df)
supervised_probabilities = rf_model.predict_proba(X_new_scaled_df)

# Unsupervised Detection (Isolation Forest)
print("--- Unsupervised Detection (Anomaly Scoring) ---")
anomaly_scores = iso_model.decision_function(X_new_scaled_df)
anomaly_labels = iso_model.predict(X_new_scaled_df)


# --- 5. Report Findings ---
print("\n--- Detection Results ---")

results = []
for i in range(num_simulated_flows):
    pred_label = "ATTACK" if supervised_predictions[i] == 1 else "BENIGN"
    attack_prob = supervised_probabilities[i][1]

    anomaly_status = "ANOMALY" if anomaly_labels[i] == -1 else "NORMAL"
    iso_score = anomaly_scores[i]

    # Combined Decision Logic
    combined_status = "NORMAL" # Default
    if pred_label == "ATTACK" and anomaly_status == "ANOMALY":
        combined_status = "CRITICAL ALERT (Confirmed Attack & Anomaly)"
    elif pred_label == "ATTACK" and anomaly_status == "NORMAL":
        combined_status = "HIGH ALERT (Known Attack Signature)"
    elif pred_label == "BENIGN" and anomaly_status == "ANOMALY":
        combined_status = "MEDIUM ALERT (Unusual Activity - Potential Zero-Day/Misconfig)"
    else: # BENIGN and NORMAL
        combined_status = "NORMAL"


    results.append({
        "Flow_ID": i,
        "RF_Prediction": pred_label,
        "RF_Attack_Prob": f"{attack_prob:.4f}",
        "IF_Status": anomaly_status,
        "IF_Score": f"{iso_score:.4f}",
        "Combined_Status": combined_status
    })

results_df = pd.DataFrame(results)
print(results_df)

print("\n--- Summary of Detection ---")
print("Supervised (RF) Predictions:\n", results_df['RF_Prediction'].value_counts())
print("\nUnsupervised (IF) Status:\n", results_df['IF_Status'].value_counts())
print("\nCombined Detection Status:\n", results_df['Combined_Status'].value_counts())

print("\n--- Detection Agent Finished ---")