import pandas as pd
import numpy as np
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_dummy_data(output_path="data/processed/dummy_cicids_data.csv", n_samples=1000, n_anomalies=100, random_state=42):
    logger.info("--- Generating Realistic Dummy CICIDS-like Data ---")
    np.random.seed(random_state)

    # These are the 36 features common to CICIDS and what your model expects
    feature_names = [
        'Total Fwd Packets', 'Total Backward Packets', 'Total Length of Fwd Packets',
        'Total Length of Bwd Packets', 'Fwd Packet Length Max', 'Fwd Packet Length Min',
        'Fwd Packet Length Mean', 'Fwd Packet Length Std', 'Bwd Packet Length Max',
        'Bwd Packet Length Min', 'Bwd Packet Length Mean', 'Bwd Packet Length Std',
        'Flow Bytes/s', 'Flow Packets/s', 'Flow IAT Mean', 'Flow IAT Std',
        'Flow IAT Max', 'Flow IAT Min', 'Fwd IAT Total', 'Fwd IAT Mean',
        'Fwd IAT Std', 'Fwd IAT Max', 'Fwd IAT Min', 'Bwd IAT Total',
        'Bwd IAT Mean', 'Bwd IAT Std', 'Bwd IAT Max', 'Bwd IAT Min',
        'Active Mean', 'Active Std', 'Active Max', 'Active Min', 'Idle Mean',
        'Idle Std', 'Idle Max', 'Idle Min'
    ]

    # Generate realistic 'normal' data (Label = 1)
    # Use different realistic ranges for different types of features
    normal_data = []
    for i in range(n_samples - n_anomalies):
        row = []
        for j, feature in enumerate(feature_names):
            if 'Packets' in feature:
                # Packet counts: typically 1-50 for normal flows
                value = np.random.exponential(scale=8) + 1
            elif 'Length' in feature or 'Bytes' in feature:
                # Byte/length features: typically 0-1500 bytes
                value = np.random.exponential(scale=200) + np.random.normal(50, 20)
            elif 'IAT' in feature or 'Flow' in feature:
                # Inter-arrival times and flow features: microseconds/milliseconds
                value = np.random.exponential(scale=1000) + np.random.normal(100, 50)
            elif 'Active' in feature or 'Idle' in feature:
                # Active/Idle times: typically small values
                value = np.random.exponential(scale=50) + np.random.normal(10, 5)
            else:
                # Default case
                value = np.random.exponential(scale=100) + np.random.normal(50, 20)

            row.append(max(0, value))  # Ensure non-negative
        normal_data.append(row)

    normal_df = pd.DataFrame(normal_data, columns=feature_names)
    normal_df['Label'] = 1  # 1 for normal traffic

    # Generate realistic 'anomaly' data (Label = 0)
    # Create different types of anomalies that represent real attack patterns
    anomaly_data = []
    anomaly_types = []
    for i in range(n_anomalies):
        # Choose anomaly type randomly
        anomaly_type = np.random.choice(['ddos', 'port_scan', 'data_exfiltration', 'slow_attack'])
        anomaly_types.append(anomaly_type)

        if anomaly_type == 'ddos':
            # DDoS: High packet counts, high bytes/s, short IATs
            row = []
            for j, feature in enumerate(feature_names):
                if 'Packets' in feature:
                    value = np.random.exponential(scale=100) + 50  # Much higher packet counts
                elif 'Bytes' in feature or 'Length' in feature:
                    value = np.random.exponential(scale=800) + 200  # Higher byte counts
                elif 'IAT' in feature:
                    value = np.random.exponential(scale=10) + 1  # Very short inter-arrival times
                elif 'Flow' in feature and 'Packets' in feature:
                    value = np.random.exponential(scale=200) + 100  # High flow rates
                else:
                    value = np.random.exponential(scale=150) + np.random.normal(100, 30)
                row.append(max(0, value))

        elif anomaly_type == 'port_scan':
            # Port Scan: Many small packets, varied IATs
            row = []
            for j, feature in enumerate(feature_names):
                if 'Packets' in feature:
                    value = np.random.exponential(scale=30) + 20  # Moderate packet counts
                elif 'Length' in feature:
                    value = np.random.exponential(scale=50) + 10  # Small packet sizes
                elif 'IAT' in feature:
                    value = np.random.exponential(scale=500) + np.random.normal(200, 100)  # Varied IATs
                else:
                    value = np.random.exponential(scale=80) + np.random.normal(40, 15)
                row.append(max(0, value))

        elif anomaly_type == 'data_exfiltration':
            # Data Exfiltration: Large data transfers, long connections
            row = []
            for j, feature in enumerate(feature_names):
                if 'Length' in feature or 'Bytes' in feature:
                    value = np.random.exponential(scale=2000) + 500  # Large data transfers
                elif 'Active' in feature:
                    value = np.random.exponential(scale=500) + 100  # Long active times
                elif 'Packets' in feature:
                    value = np.random.exponential(scale=50) + 10
                else:
                    value = np.random.exponential(scale=200) + np.random.normal(150, 50)
                row.append(max(0, value))

        else:  # slow_attack
            # Slow Attack: Low rates but unusual patterns
            row = []
            for j, feature in enumerate(feature_names):
                if 'IAT' in feature:
                    value = np.random.exponential(scale=5000) + 1000  # Very long IATs
                elif 'Packets' in feature:
                    value = np.random.exponential(scale=5) + 1  # Very few packets
                else:
                    value = np.random.exponential(scale=50) + np.random.normal(20, 10)
                row.append(max(0, value))

        anomaly_data.append(row)

    anomaly_df = pd.DataFrame(anomaly_data, columns=feature_names)
    anomaly_df['Label'] = 0  # 0 for anomaly traffic

    # Combine and shuffle the data
    dummy_df = pd.concat([normal_df, anomaly_df]).sample(frac=1, random_state=random_state).reset_index(drop=True)

    # Ensure all values are non-negative and reasonable
    dummy_df[feature_names] = dummy_df[feature_names].apply(lambda x: x.clip(lower=0))

    # Create processed directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    dummy_df.to_csv(output_path, index=False)

    # Log statistics
    logger.info(f"Dummy data generated with shape: {dummy_df.shape}")
    logger.info(f"Normal samples: {n_samples - n_anomalies}, Anomaly samples: {n_anomalies}")

    # Log anomaly type distribution
    from collections import Counter
    anomaly_type_counts = Counter(anomaly_types)
    logger.info(f"Anomaly types distribution: {dict(anomaly_type_counts)}")

    # Log some basic statistics
    logger.info(f"Label distribution: {dummy_df['Label'].value_counts().to_dict()}")
    logger.info(f"Feature value ranges (min-max):")
    for feature in feature_names[:5]:  # Show first 5 features as example
        min_val = dummy_df[feature].min()
        max_val = dummy_df[feature].max()
        logger.info(f"  {feature}: {min_val:.2f} - {max_val:.2f}")

    logger.info(f"Dummy data saved to {output_path}")
    logger.info("Realistic dummy data generation completed.")

if __name__ == "__main__":
    generate_dummy_data()