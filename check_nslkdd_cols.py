# check_nslkdd_cols.py
import pandas as pd
import os

try:
    df_nslkdd = pd.read_csv("data/processed/nslkdd_processed.csv")
    print("Columns in data/processed/nslkdd_processed.csv:")
    print(df_nslkdd.columns.tolist())
    print(f"Number of columns (excluding Label): {len(df_nslkdd.columns) - 1}")
except FileNotFoundError:
    print("Error: data/processed/nslkdd_processed.csv not found. Please run data_preprocessor.py first.")