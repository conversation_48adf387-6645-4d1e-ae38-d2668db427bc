import pandas as pd
import numpy as np
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_dummy_data(output_path="data/processed/dummy_cicids_data.csv", n_samples=1000, n_anomalies=100, random_state=42):
    logger.info("--- Generating Dummy CICIDS-like Data ---")
    np.random.seed(random_state)

    # These are the 36 features common to CICIDS and what your model expects
    feature_names = [
        'Total Fwd Packets', 'Total Backward Packets', 'Total Length of Fwd Packets',
        'Total Length of Bwd Packets', 'Fwd Packet Length Max', 'Fwd Packet Length Min',
        'Fwd Packet Length Mean', 'Fwd Packet Length Std', 'Bwd Packet Length Max',
        'Bwd Packet Length Min', 'Bwd Packet Length Mean', 'Bwd Packet Length Std',
        'Flow Bytes/s', 'Flow Packets/s', 'Flow IAT Mean', 'Flow IAT Std',
        'Flow IAT Max', 'Flow IAT Min', 'Fwd IAT Total', 'Fwd IAT Mean',
        'Fwd IAT Std', 'Fwd IAT Max', 'Fwd IAT Min', 'Bwd IAT Total',
        'Bwd IAT Mean', 'Bwd IAT Std', 'Bwd IAT Max', 'Bwd IAT Min',
        'Active Mean', 'Active Std', 'Active Max', 'Active Min', 'Idle Mean',
        'Idle Std', 'Idle Max', 'Idle Min'
    ]
    
    n_features = len(feature_names)

    # Generate 'normal' data (Label = 1)
    # Use a normal distribution around some typical values
    normal_data = np.random.normal(loc=100, scale=20, size=(n_samples - n_anomalies, n_features))
    normal_df = pd.DataFrame(normal_data, columns=feature_names)
    normal_df['Label'] = 1 # 1 for normal traffic

    # Generate 'anomaly' data (Label = 0)
    # Make anomalies significantly different (e.g., much larger values)
    anomaly_data = np.random.normal(loc=500, scale=50, size=(n_anomalies, n_features))
    anomaly_df = pd.DataFrame(anomaly_data, columns=feature_names)
    anomaly_df['Label'] = 0 # 0 for anomaly traffic

    # Combine and shuffle the data
    dummy_df = pd.concat([normal_df, anomaly_df]).sample(frac=1, random_state=random_state).reset_index(drop=True)

    # Ensure all values are non-negative
    dummy_df[feature_names] = dummy_df[feature_names].apply(lambda x: x.clip(lower=0))

    # Create processed directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    dummy_df.to_csv(output_path, index=False)
    logger.info(f"Dummy data generated with shape: {dummy_df.shape}")
    logger.info(f"Normal samples: {n_samples - n_anomalies}, Anomaly samples: {n_anomalies}")
    logger.info(f"Dummy data saved to {output_path}")
    logger.info("Dummy data generation completed.")

if __name__ == "__main__":
    generate_dummy_data()