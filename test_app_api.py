#!/usr/bin/env python3

import requests
import json
import time

def test_app_endpoints():
    """Test the Flask app endpoints"""
    base_url = "http://localhost:5000"
    
    print("=== Testing Flask App Endpoints ===")
    
    # Test if app is running
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✓ App is running and accessible")
        else:
            print(f"✗ App returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to app: {e}")
        print("Make sure the app is running with: python app.py")
        return False
    
    # Test anomaly detection endpoint
    print("\n=== Testing Anomaly Detection Endpoint ===")
    for i in range(5):
        try:
            response = requests.get(f"{base_url}/detect_anomaly", timeout=10)
            if response.status_code == 200:
                data = response.json()
                log_entry = data.get('log_entry', {})
                
                is_anomaly = log_entry.get('is_anomaly', False)
                final_prediction = log_entry.get('final_prediction', -1)
                
                status = "🚨 ANOMALY" if is_anomaly else "✅ NORMAL"
                print(f"  Test {i+1}: {status} (prediction: {final_prediction})")
                
                # Check if XAI explanation is present for anomalies
                if is_anomaly and log_entry.get('xai_explanation'):
                    top_features = log_entry['xai_explanation'].get('top_features', [])
                    print(f"    XAI: {len(top_features)} features explained")
                
            else:
                print(f"  ✗ Test {i+1} failed with status: {response.status_code}")
                print(f"    Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ✗ Test {i+1} failed: {e}")
        
        time.sleep(1)
    
    # Test logs endpoint
    print("\n=== Testing Logs Endpoint ===")
    try:
        response = requests.get(f"{base_url}/get_latest_logs", timeout=5)
        if response.status_code == 200:
            logs = response.json()
            print(f"✓ Retrieved {len(logs)} log entries")
            
            if logs:
                anomaly_count = sum(1 for log in logs if log.get('is_anomaly', False))
                print(f"  Anomalies in logs: {anomaly_count}/{len(logs)}")
        else:
            print(f"✗ Logs endpoint failed with status: {response.status_code}")
            print(f"  Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Logs endpoint failed: {e}")
    
    print("\n=== Test Complete ===")
    print("If you see this without errors, the Flask app is working correctly!")
    return True

if __name__ == "__main__":
    test_app_endpoints()
