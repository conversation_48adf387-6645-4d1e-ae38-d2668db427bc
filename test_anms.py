# test_anms.py (Revised for Cleaner Confusion Matrix String)

import pandas as pd
import numpy as np
import os
import pickle
import logging
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
from src.decision_agent import DecisionAgent
from datetime import datetime
from src.utils.xai_explainer import XAI_Explainer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ANMSTester:
    # ... (init and _load_artifacts, load_test_data methods remain the same) ...
    def __init__(self, data_dir='data', models_dir='models', log_dir='log', enable_xai=False, xai_sample_limit=10, xai_background_samples=100):
        self.data_dir = data_dir
        self.models_dir = models_dir
        self.log_dir = log_dir # New: Log directory
        self.enable_xai = enable_xai
        self.xai_sample_limit = xai_sample_limit # Limit XAI explanations for a few samples
        self.xai_background_samples = xai_background_samples # Number of samples for SHAP background data
        
        self.base_proj_dir = os.path.dirname(os.path.abspath(__file__)) # ANMS_Project root
        self.data_path = os.path.join(self.base_proj_dir, self.data_dir)
        self.models_path = os.path.join(self.base_proj_dir, self.models_dir)
        self.log_path = os.path.join(self.base_proj_dir, self.log_dir) # New: Log path

        # Ensure log directory exists
        if not os.path.exists(self.log_path):
            os.makedirs(self.log_path)

        self.rf_model = None
        self.iso_model = None
        self.scaler = None
        self.feature_columns = None

        self.decision_agent = None
        self.xai_explainer = None
        
        self._load_artifacts()
        
    def _load_artifacts(self):
        logger.info("--- Loading Models and Scaler ---")
        try:
            with open(os.path.join(self.models_path, 'rf_model.pkl'), 'rb') as f:
                self.rf_model = pickle.load(f)
            with open(os.path.join(self.models_path, 'iso_model.pkl'), 'rb') as f:
                self.iso_model = pickle.load(f)
            with open(os.path.join(self.models_path, 'scaler.pkl'), 'rb') as f:
                self.scaler = pickle.load(f)
            with open(os.path.join(self.models_path, 'feature_columns.pkl'), 'rb') as f:
                self.feature_columns = pickle.load(f)

            self.decision_agent = DecisionAgent(self.rf_model, self.iso_model, self.scaler, self.feature_columns)
            logger.info("Models and Scaler loaded successfully and initialized DecisionAgent.")

            if self.enable_xai:
                logger.info(f"Loading {self.xai_background_samples} samples for XAI background data...")
                X_train_full = pd.read_csv(os.path.join(self.data_path, 'X_train_preprocessed.csv'))
                
                if X_train_full.shape[0] > self.xai_background_samples:
                    background_data = X_train_full.sample(n=self.xai_background_samples, random_state=42)
                else:
                    background_data = X_train_full
                
                self.xai_explainer = XAI_Explainer(self.rf_model, self.feature_columns)
                
                def rf_predict_proba_scaled(X_input_raw):
                    X_df = pd.DataFrame(X_input_raw, columns=self.feature_columns)
                    X_scaled = self.scaler.transform(X_df)
                    return self.rf_model.predict_proba(X_scaled)

                self.xai_explainer.build_explainer(background_data, prediction_function=rf_predict_proba_scaled)
                logger.info("XAI Explainer initialized and SHAP explainer built.")

        except FileNotFoundError as e:
            logger.error(f"Required model or scaler file not found: {e}. Ensure models were trained and saved.")
            exit()
        except Exception as e:
            logger.exception(f"Error loading models or scaler or initializing XAI: {e}")
            exit()

    def load_test_data(self, X_file='X_test_preprocessed.csv', y_file='y_test_labels.csv'):
        logger.info(f"--- Loading Test Data: {X_file} and {y_file} ---")
        try:
            X_test = pd.read_csv(os.path.join(self.data_path, X_file))
            y_test = pd.read_csv(os.path.join(self.data_path, y_file)).squeeze()
            if list(X_test.columns) != self.feature_columns:
                 logger.warning("Test data columns do not match expected feature columns. Reindexing.")
                 X_test = X_test.reindex(columns=self.feature_columns, fill_value=0)
            logger.info(f"Test data loaded. X_test shape: {X_test.shape}, y_test shape: {y_test.shape}")
            return X_test, y_test
        except FileNotFoundError as e:
            logger.error(f"Test data file not found: {e}. Ensure preprocessor was run and files exist.")
            return None, None
        except Exception as e:
            logger.exception(f"Error loading test data: {e}")
            return None, None


    def evaluate_system(self, X_test, y_true, run_id=None):
        """
        Evaluates the ANMS system and returns a dictionary of metrics,
        including a string representation of the confusion matrix and classification report,
        and (if enabled) a string of top XAI features for a subset of samples.
        """
        logger.info("--- Evaluating ANMS System ---")
        if self.decision_agent is None:
            logger.error("DecisionAgent not initialized. Cannot evaluate.")
            return {}

        y_pred, combined_scores, rf_probs, iso_scores = self.decision_agent.predict(X_test)

        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='binary', zero_division=0)
        recall = recall_score(y_true, y_pred, average='binary', zero_division=0)
        f1 = f1_score(y_true, y_pred, average='binary', zero_division=0)
        conf_matrix = confusion_matrix(y_true, y_pred)
        
        # New: Format confusion matrix carefully for eval() later
        # Convert each row to a list of integers, then join with comma and wrap in brackets
        # Example: [[0,0],[350,22194]]
        conf_matrix_formatted = str(conf_matrix.tolist()) # This produces [[0, 0], [350, 22194]] which is valid Python list syntax

        class_report_str = classification_report(y_true, y_pred, zero_division=0)

        # Log results to console
        logger.info(f"\n--- Evaluation Results (Run {run_id if run_id is not None else 'N/A'}) ---")
        logger.info(f"Accuracy: {accuracy:.4f}")
        logger.info(f"Precision: {precision:.4f}")
        logger.info(f"Recall: {recall:.4f}")
        logger.info(f"F1-Score: {f1:.4f}")
        logger.info(f"Confusion Matrix:\n{conf_matrix}")
        logger.info(f"Classification Report:\n{class_report_str}")
        
        # New: Collect XAI explanations for CSV
        xai_explanations_str = "N/A" # Default if XAI is not enabled or fails
        if self.enable_xai and self.xai_explainer:
            logger.info(f"Generating XAI explanations for first {self.xai_sample_limit} samples...")
            all_xai_samples = []
            for i in range(min(self.xai_sample_limit, X_test.shape[0])):
                instance = X_test.iloc[i]
                try:
                    explanation = self.xai_explainer.explain_instance(instance)
                    if explanation:
                        top_features = self.xai_explainer.get_top_features(explanation, num_features=5)
                        
                        # Format top features for CSV: "SampleX: Feature1 (val1), Feature2 (val2); "
                        sample_xai_str = f"Sample {i+1}: " + ", ".join([f"{f} ({v:.2f})" for f, v in top_features])
                        all_xai_samples.append(sample_xai_str)
                        logger.info(f"Top 5 XAI features for Sample {i+1}: {', '.join([f'{f} ({v:.2f})' for f,v in top_features])}")
                    else:
                        logger.warning(f"No explanation generated for sample {i}.")
                except Exception as e:
                    logger.error(f"Error generating XAI for sample {i}: {e}")
            
            if all_xai_samples:
                xai_explanations_str = " || ".join(all_xai_samples)

        # Return metrics, including XAI string
        return {
            'run_id': run_id,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'confusion_matrix': conf_matrix_formatted, # Use the new formatted string here
            'classification_report': class_report_str.replace('\n', ' | '),
            'xai_top_features_summary': xai_explanations_str
        }

    # ... (save_results_to_csv and run_targeted_tests methods remain the same) ...
    def save_results_to_csv(self, results_list, filename="test_results.csv"):
        df_results = pd.DataFrame(results_list)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        full_path = os.path.join(self.log_path, f"{timestamp}_{filename}")
        df_results.to_csv(full_path, index=False)
        logger.info(f"Detailed test results saved to: {full_path}")


def run_targeted_tests(num_runs=10, test_file_prefix='X_test_preprocessed', label_file_prefix='y_test_labels', enable_xai=True, xai_limit=5, xai_background_samples=100):
    logger.info(f"\n--- Starting {num_runs} Targeted ANMS Test Runs ---")

    tester = ANMSTester(enable_xai=enable_xai, xai_sample_limit=xai_limit, xai_background_samples=xai_background_samples)
    
    X_test_full, y_test_full = tester.load_test_data(
        X_file=f'{test_file_prefix}.csv',
        y_file=f'{label_file_prefix}.csv'
    )
    if X_test_full is None:
        logger.error("Failed to load full test data. Exiting targeted tests.")
        return

    all_metrics_for_csv = []
    all_summary_metrics = []

    for run_num in range(num_runs):
        logger.info(f"\n--- Running Test Iteration {run_num + 1}/{num_runs} ---")
        
        metrics = tester.evaluate_system(X_test_full, y_test_full, run_id=run_num + 1)
        if metrics:
            all_metrics_for_csv.append(metrics)
            
            summary_metrics = {k: v for k, v in metrics.items() if k in ['accuracy', 'precision', 'recall', 'f1_score']}
            all_summary_metrics.append(summary_metrics)
        else:
            logger.error(f"Skipping run {run_num + 1} due to evaluation error.")

    if all_metrics_for_csv:
        tester.save_results_to_csv(all_metrics_for_csv, filename="nsl_kdd_test_report.csv")
    else:
        logger.warning("No detailed metrics were collected for CSV saving.")

    if all_summary_metrics:
        logger.info(f"\n--- Aggregated Results Over {num_runs} Runs ---")
        avg_accuracy = np.mean([m['accuracy'] for m in all_summary_metrics])
        avg_precision = np.mean([m['precision'] for m in all_summary_metrics])
        avg_recall = np.mean([m['recall'] for m in all_summary_metrics])
        avg_f1 = np.mean([m['f1_score'] for m in all_summary_metrics])

        logger.info(f"Average Accuracy: {avg_accuracy:.4f}")
        logger.info(f"Average Precision: {avg_precision:.4f}")
        logger.info(f"Average Recall: {avg_recall:.4f}")
        logger.info(f"Average F1-Score: {avg_f1:.4f}")

        # This line will now correctly parse the string that looks like "[[0, 0], [350, 22194]]"
        sum_conf_matrix = np.sum([np.array(eval(m['confusion_matrix'])) for m in all_metrics_for_csv], axis=0)
        logger.info(f"Summed Confusion Matrix (Over {num_runs} runs):\n{sum_conf_matrix}")
    else:
        logger.warning("No summary metrics were collected from any run.")


if __name__ == '__main__':
    run_targeted_tests(num_runs=10, enable_xai=True, xai_limit=5, xai_background_samples=100)