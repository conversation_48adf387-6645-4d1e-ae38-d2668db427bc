#!/usr/bin/env python3

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import io
import base64
import os

def create_real_time_plots(detection_logs, plots_dir="static/plots"):
    """Create real-time plots from detection logs"""
    os.makedirs(plots_dir, exist_ok=True)
    
    if not detection_logs:
        return None
    
    # Convert logs to DataFrame
    df_logs = pd.DataFrame(detection_logs)
    df_logs['timestamp'] = pd.to_datetime(df_logs['timestamp'])
    
    # Create plots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Real-Time ANMS Dashboard', fontsize=16, fontweight='bold')
    
    # 1. Detection timeline
    df_logs['hour'] = df_logs['timestamp'].dt.floor('H')
    hourly_counts = df_logs.groupby(['hour', 'is_anomaly']).size().unstack(fill_value=0)
    
    if len(hourly_counts) > 0:
        hourly_counts.plot(kind='bar', ax=axes[0, 0], color=['#4ecdc4', '#ff6b6b'])
        axes[0, 0].set_title('Detections Over Time')
        axes[0, 0].set_xlabel('Time')
        axes[0, 0].set_ylabel('Count')
        axes[0, 0].legend(['Normal', 'Anomaly'])
        axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 2. Model agreement
    agreement = []
    for log in detection_logs:
        sup_pred = log.get('supervised_prediction', 1)
        unsup_pred = log.get('unsupervised_prediction', 1)
        if sup_pred == unsup_pred:
            agreement.append('Agree')
        else:
            agreement.append('Disagree')
    
    agreement_counts = pd.Series(agreement).value_counts()
    axes[0, 1].pie(agreement_counts.values, labels=agreement_counts.index, autopct='%1.1f%%',
                   colors=['#4ecdc4', '#ff9999'])
    axes[0, 1].set_title('Model Agreement')
    
    # 3. Anomaly score distribution
    sup_scores = [log.get('supervised_proba_anomaly', 0) for log in detection_logs]
    unsup_scores = [log.get('unsupervised_anomaly_score', 0) for log in detection_logs]
    
    axes[1, 0].hist(sup_scores, bins=20, alpha=0.7, label='Supervised', color='#4ecdc4')
    axes[1, 0].set_xlabel('Anomaly Probability')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].set_title('Supervised Model Scores')
    
    axes[1, 1].hist(unsup_scores, bins=20, alpha=0.7, label='Unsupervised', color='#ff6b6b')
    axes[1, 1].set_xlabel('Anomaly Score')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Unsupervised Model Scores')
    
    plt.tight_layout()
    
    # Save plot
    plot_path = f'{plots_dir}/realtime_dashboard.png'
    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    return plot_path

def create_detection_summary_plot(detection_logs, plots_dir="static/plots"):
    """Create a summary plot of recent detections"""
    os.makedirs(plots_dir, exist_ok=True)
    
    if not detection_logs:
        return None
    
    # Get recent logs (last 50)
    recent_logs = detection_logs[-50:] if len(detection_logs) > 50 else detection_logs
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle('Detection Summary (Last 50 Detections)', fontsize=14, fontweight='bold')
    
    # 1. Anomaly vs Normal count
    anomaly_count = sum(1 for log in recent_logs if log.get('is_anomaly', False))
    normal_count = len(recent_logs) - anomaly_count
    
    axes[0].bar(['Normal', 'Anomaly'], [normal_count, anomaly_count], 
                color=['#4ecdc4', '#ff6b6b'])
    axes[0].set_title('Detection Counts')
    axes[0].set_ylabel('Count')
    
    # Add count labels on bars
    for i, v in enumerate([normal_count, anomaly_count]):
        axes[0].text(i, v + 0.5, str(v), ha='center', va='bottom')
    
    # 2. Final prediction distribution
    final_preds = [log.get('final_prediction', 1) for log in recent_logs]
    pred_counts = pd.Series(final_preds).value_counts()
    
    axes[1].pie(pred_counts.values, labels=['Normal' if x == 1 else 'Anomaly' for x in pred_counts.index], 
                autopct='%1.1f%%', colors=['#4ecdc4', '#ff6b6b'])
    axes[1].set_title('Final Predictions')
    
    # 3. Detection timeline (last 20)
    recent_20 = recent_logs[-20:] if len(recent_logs) > 20 else recent_logs
    timeline_data = [1 if log.get('is_anomaly', False) else 0 for log in recent_20]
    
    axes[2].plot(range(len(timeline_data)), timeline_data, 'o-', color='#ff6b6b', markersize=6)
    axes[2].fill_between(range(len(timeline_data)), timeline_data, alpha=0.3, color='#ff6b6b')
    axes[2].set_title('Recent Detection Timeline')
    axes[2].set_xlabel('Detection #')
    axes[2].set_ylabel('Anomaly (1) / Normal (0)')
    axes[2].set_ylim(-0.1, 1.1)
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    plot_path = f'{plots_dir}/detection_summary.png'
    plt.savefig(plot_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    return plot_path

def plot_to_base64(plot_path):
    """Convert plot to base64 string for embedding in HTML"""
    try:
        with open(plot_path, 'rb') as img_file:
            img_data = img_file.read()
            img_base64 = base64.b64encode(img_data).decode('utf-8')
            return f"data:image/png;base64,{img_base64}"
    except:
        return None

# Example usage function
def test_plotting():
    """Test the plotting functions with sample data"""
    print("Testing real-time plotting functions...")
    
    # Create sample detection logs
    sample_logs = []
    for i in range(30):
        is_anomaly = np.random.random() < 0.2  # 20% anomalies
        log = {
            'timestamp': (datetime.now() - timedelta(hours=i)).isoformat(),
            'is_anomaly': is_anomaly,
            'final_prediction': 0 if is_anomaly else 1,
            'supervised_prediction': 0 if is_anomaly else 1,
            'unsupervised_prediction': 0 if is_anomaly else 1,
            'supervised_proba_anomaly': np.random.random() if is_anomaly else np.random.random() * 0.3,
            'unsupervised_anomaly_score': np.random.normal(-0.2, 0.1) if is_anomaly else np.random.normal(0.1, 0.05)
        }
        sample_logs.append(log)
    
    # Create plots
    os.makedirs('static/plots', exist_ok=True)
    
    plot1 = create_real_time_plots(sample_logs)
    plot2 = create_detection_summary_plot(sample_logs)
    
    print(f"Real-time dashboard plot saved: {plot1}")
    print(f"Detection summary plot saved: {plot2}")
    print("Test completed successfully!")

if __name__ == "__main__":
    test_plotting()
