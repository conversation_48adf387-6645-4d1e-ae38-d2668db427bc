# src/data_processing/nsl_kdd_preprocessor.py

import pandas as pd
import numpy as np
import os
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# In src/data_processing/nsl_kdd_preprocessor.py

# ... (imports and logging setup)

# In src/data_processing/nsl_kdd_preprocessor.py

# ... (imports and logging setup)

def preprocess_nsl_kdd(
    raw_data_dir='data/nsl-kdd',
    output_data_dir='data',
    output_models_dir='models'
):
    logger.info("Starting NSL-KDD data preprocessing...")

    # CORRECTED LINE: Go up two levels from the script's directory to reach the project root
    base_proj_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    full_raw_data_path = os.path.join(base_proj_dir, raw_data_dir)
    full_output_data_path = os.path.join(base_proj_dir, output_data_dir)
    full_output_models_path = os.path.join(base_proj_dir, output_models_dir)

    logger.info(f"Looking for NSL-KDD data in: {full_raw_data_path}")

    # Define NSL-KDD column names
    feature_names = [
        'duration', 'protocol_type', 'service', 'flag', 'src_bytes', 'dst_bytes',
        'land', 'wrong_fragment', 'urgent', 'hot', 'num_failed_logins',
        'logged_in', 'num_compromised', 'root_shell', 'su_attempted', 'num_root',
        'num_file_creations', 'num_shells', 'num_access_files', 'num_outbound_cmds',
        'is_host_login', 'is_guest_login', 'count', 'srv_count', 'serror_rate',
        'srv_serror_rate', 'rerror_rate', 'srv_rerror_rate', 'same_srv_rate',
        'diff_srv_rate', 'srv_diff_host_rate', 'dst_host_count', 'dst_host_srv_count',
        'dst_host_same_srv_rate', 'dst_host_diff_srv_rate', 'dst_host_same_src_port_rate',
        'dst_host_srv_diff_host_rate', 'dst_host_serror_rate', 'dst_host_srv_serror_rate',
        'dst_host_rerror_rate', 'dst_host_srv_rerror_rate', 'attack_type', 'difficulty'
    ]

    try:
        train_df = pd.read_csv(os.path.join(full_raw_data_path, 'KDDTrain+.txt'), names=feature_names)
        logger.info(f"Loaded KDDTrain+.txt with shape: {train_df.shape}")

        test_feature_names = [f for f in feature_names if f != 'difficulty']
        test_df = pd.read_csv(os.path.join(full_raw_data_path, 'KDDTest+.txt'), names=test_feature_names)
        logger.info(f"Loaded KDDTest+.txt with shape: {test_df.shape}")

    except FileNotFoundError as e:
        logger.error(f"NSL-KDD raw data file not found: {e}. Please ensure KDDTrain+.txt and KDDTest+.txt are in {full_raw_data_path}")
        return
    except Exception as e:
        logger.exception(f"Error loading NSL-KDD raw data: {e}")
        return

    # --- 1. Label Mapping (Attack vs. Benign) ---
    train_df['binary_label'] = train_df['attack_type'].apply(lambda x: 0 if x == 'normal' else 1)
    test_df['binary_label'] = test_df['attack_type'].apply(lambda x: 0 if x == 'normal' else 1)
    logger.info(f"Mapped labels to binary (0: Benign, 1: Attack).")
    logger.info(f"Train Benign: {train_df[train_df['binary_label'] == 0].shape[0]}, Attack: {train_df[train_df['binary_label'] == 1].shape[0]}")
    logger.info(f"Test Benign: {test_df[test_df['binary_label'] == 0].shape[0]}, Attack: {test_df[test_df['binary_label'] == 1].shape[0]}")


    # --- 2. Feature Engineering (Categorical to Numerical) ---
    # These are the *only* categorical columns in NSL-KDD that need one-hot encoding.
    categorical_cols = ['protocol_type', 'service', 'flag']

    # Separate features (X) from labels (y) before encoding
    X_train_raw = train_df.drop(columns=['attack_type', 'difficulty', 'binary_label'])
    y_train = train_df['binary_label']

    X_test_raw = test_df.drop(columns=['attack_type', 'binary_label']) # Test set doesn't have 'difficulty'
    y_test = test_df['binary_label']

    # Combine train and test for consistent encoding (fit on all unique values from both)
    # This prevents issues if a category appears only in test but not train
    combined_X = pd.concat([X_train_raw, X_test_raw], ignore_index=True)
    
    # Apply one-hot encoding to the combined dataset
    for col in categorical_cols:
        logger.info(f"Encoding categorical column: {col}")
        # pd.get_dummies will create new columns for each category
        dummies = pd.get_dummies(combined_X[col], prefix=col, dtype=int)
        combined_X = pd.concat([combined_X.drop(columns=[col]), dummies], axis=1)

    # Split back into train and test after encoding
    processed_train_X = combined_X.iloc[:len(X_train_raw)]
    processed_test_X = combined_X.iloc[len(X_train_raw):]

    # Ensure all columns are numeric after one-hot encoding.
    # This specifically handles any non-numeric columns that might have slipped through,
    # converting them to numeric, coercing errors to NaN, then filling NaNs.
    for col in processed_train_X.columns:
        processed_train_X[col] = pd.to_numeric(processed_train_X[col], errors='coerce')
    processed_train_X = processed_train_X.fillna(0) # Fill any NaNs created by coercion
    
    for col in processed_test_X.columns:
        processed_test_X[col] = pd.to_numeric(processed_test_X[col], errors='coerce')
    processed_test_X = processed_test_X.fillna(0) # Fill any NaNs created by coercion

    logger.info(f"One-hot encoded categorical features and ensured all columns are numeric. New feature count: {processed_train_X.shape[1]}")

    # --- 3. Scaling Numerical Features ---
    scaler = StandardScaler()

    # Fit scaler ONLY on training data
    X_train_scaled = scaler.fit_transform(processed_train_X)
    X_test_scaled = scaler.transform(processed_test_X)

    # Convert back to DataFrame, keeping column names
    X_train_final = pd.DataFrame(X_train_scaled, columns=processed_train_X.columns)
    X_test_final = pd.DataFrame(X_test_scaled, columns=processed_test_X.columns)

    logger.info(f"Scaled numerical features. X_train_final shape: {X_train_final.shape}, X_test_final shape: {X_test_final.shape}")

    # --- 4. Save Processed Data and Artifacts ---
    # Save preprocessed X_train, y_train, X_test, y_test
    X_train_final.to_csv(os.path.join(full_output_data_path, 'X_train_preprocessed.csv'), index=False)
    y_train.to_csv(os.path.join(full_output_data_path, 'y_train_labels.csv'), index=False)
    X_test_final.to_csv(os.path.join(full_output_data_path, 'X_test_preprocessed.csv'), index=False)
    y_test.to_csv(os.path.join(full_output_data_path, 'y_test_labels.csv'), index=False)
    logger.info(f"Saved preprocessed data to {full_output_data_path}")

    # Save scaler and feature columns for later use by the ANMS agents
    with open(os.path.join(full_output_models_path, 'scaler.pkl'), 'wb') as f:
        pickle.dump(scaler, f)
    logger.info(f"Saved scaler to {full_output_models_path}/scaler.pkl")

    with open(os.path.join(full_output_models_path, 'feature_columns.pkl'), 'wb') as f:
        pickle.dump(X_train_final.columns.tolist(), f)
    logger.info(f"Saved feature columns to {full_output_models_path}/feature_columns.pkl")

    logger.info("NSL-KDD data preprocessing complete.")


if __name__ == '__main__':
    preprocess_nsl_kdd() # Call the main preprocessing function