# decision_agent.py
import numpy as np
import logging
import pandas as pd
from supervised_agent import SupervisedAgent
from unsupervised_agent import UnsupervisedAgent
from src.utils.xai_explainer import XAI_Explainer # Corrected import path for your xai_explainer.py

logger = logging.getLogger(__name__)

class DecisionAgent:
    def __init__(self, feature_names,
                 supervised_model_path="models/supervised_model.pkl",
                 unsupervised_model_path="models/unsupervised_model.pkl",
                 scaler_path="models/scaler.pkl"):
        """
        Initializes the Decision Agent with Supervised and Unsupervised Agents,
        and the XAI Explainer.
        """
        self.supervised_agent = SupervisedAgent(supervised_model_path)
        self.unsupervised_agent = UnsupervisedAgent(unsupervised_model_path, scaler_path)
        self.xai_explainer = None # Will be initialized later once background data is available
        self.feature_names = feature_names # Store feature names for XAI
        logger.info("DecisionAgent initialized.")

    def initialize_xai_explainer(self, model, background_data_for_shap, target_class_index=0):
        """
        Initializes the XAI Explainer. This must be called after models are trained
        and you have access to representative background data (e.g., X_train).
        """
        # Pass the actual model instance (RandomForestClassifier) to XAI_Explainer
        # The XAI_Explainer will then internally use model.predict_proba for the specified class.
        self.xai_explainer = XAI_Explainer(model, self.feature_names)
        self.xai_explainer.build_explainer(background_data_for_shap, target_class_index)
        logger.info(f"XAI Explainer initialized for target class index {target_class_index}.")


    def make_decision(self, instance_data):
        """
        Makes a combined decision using predictions from both agents and provides XAI explanations.
        Args:
            instance_data (pd.DataFrame or np.array): A single data instance (row) to classify.
                                                      Must be a DataFrame for consistent feature names.
        Returns:
            dict: Decision result including prediction, scores, and XAI explanation (if anomaly).
        """
        if not isinstance(instance_data, pd.DataFrame):
            # Ensure it's a DataFrame with correct columns for consistency
            if isinstance(instance_data, np.ndarray):
                if instance_data.ndim == 1:
                    instance_data = pd.DataFrame([instance_data], columns=self.feature_names)
                else: # Assuming 2D, but we expect 1 row for a single instance
                    instance_data = pd.DataFrame(instance_data, columns=self.feature_names)
            else:
                logger.error("instance_data must be a pandas DataFrame or numpy array.")
                return {"prediction": -1, "reason": "Invalid input data type", "xai_explanation": None}

        # 1. Get prediction from Supervised Agent
        sup_pred = self.supervised_agent.predict(instance_data)[0]
        sup_proba = self.supervised_agent.predict_proba(instance_data)[0] # [prob_class_0, prob_class_1]

        # 2. Get prediction from Unsupervised Agent
        # Isolation Forest outputs -1 for anomaly, 1 for normal. Convert to 0/1 for consistency.
        un_pred_raw = self.unsupervised_agent.predict(instance_data)[0]
        un_pred = 0 if un_pred_raw == -1 else 1 # Convert -1 (anomaly) to 0, 1 (normal) to 1

        # Get anomaly score from Isolation Forest (lower is more anomalous)
        un_decision_score = self.unsupervised_agent.decision_function(instance_data)[0]

        # 3. Voting Mechanism (Simple Majority Vote for prototype)
        # Assuming 0 is Anomaly, 1 is Normal
        votes = [sup_pred, un_pred]
        anomaly_votes = votes.count(0)
        normal_votes = votes.count(1)

        final_prediction = 0 if anomaly_votes > normal_votes else 1 # If tie, default to normal (1)

        # 4. Generate XAI Explanation if an anomaly is detected
        xai_explanation_summary = None
        if final_prediction == 0 and self.xai_explainer:
            logger.info(f"Anomaly detected. Generating XAI explanation for instance.")
            explanation = self.xai_explainer.explain_instance(instance_data.iloc[0]) # Pass the single row as Series
            if explanation:
                top_features = self.xai_explainer.get_top_features(explanation, num_features=5)
                xai_explanation_summary = {
                    "base_value": explanation.base_values,
                    "top_features": [{"name": f, "value": v} for f, v in top_features]
                }
                logger.info(f"Generated XAI explanation: {xai_explanation_summary}")
            else:
                logger.error("Failed to generate XAI explanation.")

        # Prepare decision output
        decision_log = {
            "timestamp": pd.Timestamp.now().isoformat(),
            "instance_id": hash(frozenset(instance_data.values[0])), # Simple ID for now
            "supervised_prediction": int(sup_pred),
            "supervised_proba_anomaly": float(sup_proba[0]), # Probability of class 0 (Anomaly)
            "unsupervised_prediction": int(un_pred),
            "unsupervised_anomaly_score": float(un_decision_score),
            "final_prediction": int(final_prediction),
            "is_anomaly": bool(final_prediction == 0),
            "xai_explanation": xai_explanation_summary,
            "raw_instance_data": instance_data.values[0].tolist() # For auditing/debugging
        }

        return decision_log