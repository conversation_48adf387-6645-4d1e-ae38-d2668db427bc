#!/usr/bin/env python3

print("=== Minimal ANMS Test ===")

try:
    print("1. Testing imports...")
    import pandas as pd
    import numpy as np
    import joblib
    print("   ✓ Basic imports successful")
    
    print("2. Testing model loading...")
    rf_model = joblib.load("models/supervised_model.pkl")
    iso_model = joblib.load("models/unsupervised_model.pkl")
    scaler = joblib.load("models/scaler.pkl")
    print("   ✓ Models loaded successfully")
    
    print("3. Testing data loading...")
    df = pd.read_csv("data/processed/dummy_cicids_data.csv")
    X = df.drop('Label', axis=1)
    y = df['Label']
    feature_names = X.columns.tolist()
    print(f"   ✓ Data loaded: {X.shape[0]} samples, {X.shape[1]} features")
    
    print("4. Testing predictions...")
    # Test with one normal sample
    normal_sample = X[y == 1].iloc[0:1]
    normal_scaled = scaler.transform(normal_sample)
    
    rf_pred = rf_model.predict(normal_sample)[0]
    iso_pred_raw = iso_model.predict(normal_scaled)[0]
    iso_pred = 0 if iso_pred_raw == -1 else 1
    
    print(f"   Normal sample: RF={rf_pred}, ISO={iso_pred}")
    
    # Test with one anomaly sample
    anomaly_sample = X[y == 0].iloc[0:1]
    anomaly_scaled = scaler.transform(anomaly_sample)
    
    rf_pred = rf_model.predict(anomaly_sample)[0]
    iso_pred_raw = iso_model.predict(anomaly_scaled)[0]
    iso_pred = 0 if iso_pred_raw == -1 else 1
    
    print(f"   Anomaly sample: RF={rf_pred}, ISO={iso_pred}")
    
    print("5. Testing decision agent...")
    from decision_agent import DecisionAgent
    
    decision_agent = DecisionAgent(
        feature_names=feature_names,
        supervised_model_path="models/supervised_model.pkl",
        unsupervised_model_path="models/unsupervised_model.pkl",
        scaler_path="models/scaler.pkl"
    )
    print("   ✓ Decision agent initialized")
    
    # Test decision making
    test_sample = X[y == 0].iloc[0:1]  # Use an anomaly sample
    result = decision_agent.make_decision(test_sample)
    
    print(f"   Decision result: {result['final_prediction']} (anomaly: {result['is_anomaly']})")
    print(f"   Supervised: {result['supervised_prediction']}, Unsupervised: {result['unsupervised_prediction']}")
    
    print("\n=== SUCCESS ===")
    print("✓ All core components are working!")
    print("✓ Models can detect anomalies")
    print("✓ Decision agent is functional")
    print("\nThe system is ready for use!")
    
except Exception as e:
    print(f"\n=== ERROR ===")
    print(f"✗ Test failed: {e}")
    import traceback
    traceback.print_exc()
