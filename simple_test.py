#!/usr/bin/env python3

import pandas as pd
import numpy as np
import joblib
from sklearn.preprocessing import StandardScaler

def simple_test():
    print("=== Simple Anomaly Detection Test ===")
    
    # Load models
    try:
        rf_model = joblib.load("models/supervised_model.pkl")
        iso_model = joblib.load("models/unsupervised_model.pkl")
        scaler = joblib.load("models/scaler.pkl")
        print("✓ Models loaded successfully")
    except Exception as e:
        print(f"✗ Error loading models: {e}")
        return
    
    # Load test data
    try:
        df = pd.read_csv("data/processed/dummy_cicids_data.csv")
        X = df.drop('Label', axis=1)
        y = df['Label']
        print(f"✓ Data loaded: {X.shape[0]} samples, {X.shape[1]} features")
        print(f"  Normal samples: {sum(y == 1)}, Anomaly samples: {sum(y == 0)}")
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return
    
    # Test on a few samples
    print("\n=== Testing Predictions ===")
    
    # Test normal samples
    normal_samples = X[y == 1].head(3)
    print("\nNormal Samples:")
    for i, (idx, sample) in enumerate(normal_samples.iterrows()):
        sample_df = pd.DataFrame([sample])
        sample_scaled = scaler.transform(sample_df)
        
        rf_pred = rf_model.predict(sample_df)[0]
        rf_proba = rf_model.predict_proba(sample_df)[0]
        
        iso_pred_raw = iso_model.predict(sample_scaled)[0]
        iso_pred = 0 if iso_pred_raw == -1 else 1
        iso_score = iso_model.decision_function(sample_scaled)[0]
        
        print(f"  Sample {i+1}: RF={rf_pred} (prob_anom={rf_proba[0]:.3f}), ISO={iso_pred} (score={iso_score:.3f})")
    
    # Test anomaly samples
    anomaly_samples = X[y == 0].head(3)
    print("\nAnomaly Samples:")
    for i, (idx, sample) in enumerate(anomaly_samples.iterrows()):
        sample_df = pd.DataFrame([sample])
        sample_scaled = scaler.transform(sample_df)
        
        rf_pred = rf_model.predict(sample_df)[0]
        rf_proba = rf_model.predict_proba(sample_df)[0]
        
        iso_pred_raw = iso_model.predict(sample_scaled)[0]
        iso_pred = 0 if iso_pred_raw == -1 else 1
        iso_score = iso_model.decision_function(sample_scaled)[0]
        
        print(f"  Sample {i+1}: RF={rf_pred} (prob_anom={rf_proba[0]:.3f}), ISO={iso_pred} (score={iso_score:.3f})")
    
    # Create extreme anomaly
    print("\nExtreme Anomaly Sample:")
    feature_names = X.columns.tolist()
    extreme_sample = {}
    for feature in feature_names:
        if 'Packets' in feature:
            extreme_sample[feature] = 1000  # Very high
        elif 'Length' in feature or 'Bytes' in feature:
            extreme_sample[feature] = 50000  # Very large
        elif 'IAT' in feature:
            extreme_sample[feature] = 0.1  # Very short
        else:
            extreme_sample[feature] = 5000  # Generally high
    
    extreme_df = pd.DataFrame([extreme_sample])
    extreme_scaled = scaler.transform(extreme_df)
    
    rf_pred = rf_model.predict(extreme_df)[0]
    rf_proba = rf_model.predict_proba(extreme_df)[0]
    
    iso_pred_raw = iso_model.predict(extreme_scaled)[0]
    iso_pred = 0 if iso_pred_raw == -1 else 1
    iso_score = iso_model.decision_function(extreme_scaled)[0]
    
    print(f"  Extreme: RF={rf_pred} (prob_anom={rf_proba[0]:.3f}), ISO={iso_pred} (score={iso_score:.3f})")
    
    print("\n=== Test Complete ===")
    print("Legend: 0=Anomaly, 1=Normal")
    print("ISO score: negative = anomaly, positive = normal")

if __name__ == "__main__":
    simple_test()
