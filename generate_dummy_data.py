import pandas as pd
import numpy as np
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_dummy_data(output_path="data/processed/dummy_cicids_data.csv", n_samples=1000, n_anomalies=100, random_state=42):
    logger.info("--- Generating Dummy CICIDS-like Data ---")
    np.random.seed(random_state)

    # Original feature names that the models were trained with
    feature_names = [
        'ACK Flag Count', 'Average Packet Size', 'Avg Bwd Segment Size', 'Avg Fwd Segment Size',
        'Bwd Avg Bulk Rate', 'Bwd Avg Bytes/Bulk', 'Bwd Avg Packets/Bulk', 'Bwd Header Length',
        'Bwd IAT Max', 'Bwd IAT Mean', 'Bwd IAT Min', 'Bwd IAT Std', 'Bwd IAT Total',
        'Bwd PSH Flags', 'Bwd Packet Length Max', 'Bwd Packet Length Mean', 'Bwd Packet Length Min',
        'Bwd Packet Length Std', 'Bwd Packets/s', 'Bwd URG Flags', 'CWE Flag Count',
        'Down/Up Ratio', 'ECE Flag Count', 'FIN Flag Count', 'Flow Bytes/s', 'Flow Duration',
        'Flow IAT Max', 'Flow IAT Mean', 'Flow IAT Min', 'Flow IAT Std', 'Flow Packets/s',
        'Fwd Avg Bulk Rate', 'Fwd Avg Bytes/Bulk', 'Fwd Avg Packets/Bulk', 'Fwd Header Length',
        'Fwd IAT Max', 'Fwd IAT Mean', 'Fwd IAT Min', 'Fwd IAT Std', 'Fwd IAT Total',
        'Fwd PSH Flags', 'Fwd Packet Length Max', 'Fwd Packet Length Mean', 'Fwd Packet Length Min',
        'Fwd Packet Length Std', 'Fwd Packets/s', 'Fwd URG Flags', 'Idle Max', 'Idle Mean',
        'Idle Min', 'Idle Std', 'Init Bwd Win Bytes', 'Init Fwd Win Bytes', 'Max Packet Length',
        'Min Packet Length', 'PSH Flag Count', 'Packet Length Mean', 'Packet Length Std',
        'Packet Length Variance', 'RST Flag Count', 'SYN Flag Count', 'Subflow Bwd Bytes',
        'Subflow Bwd Packets', 'Subflow Fwd Bytes', 'Subflow Fwd Packets', 'Total Backward Packets',
        'Total Fwd Packets', 'Total Length of Bwd Packets', 'Total Length of Fwd Packets',
        'URG Flag Count', 'act_data_pkt_fwd', 'min_seg_size_forward'
    ]

    # Generate 'normal' data (Label = 1) - simplified approach
    normal_data = np.random.normal(loc=100, scale=20, size=(n_samples - n_anomalies, len(feature_names)))
    normal_df = pd.DataFrame(normal_data, columns=feature_names)
    normal_df['Label'] = 1  # 1 for normal traffic

    # Generate 'anomaly' data (Label = 0) - simplified approach
    anomaly_data = np.random.normal(loc=500, scale=50, size=(n_anomalies, len(feature_names)))
    anomaly_df = pd.DataFrame(anomaly_data, columns=feature_names)
    anomaly_df['Label'] = 0  # 0 for anomaly traffic

    # Combine and shuffle the data
    dummy_df = pd.concat([normal_df, anomaly_df]).sample(frac=1, random_state=random_state).reset_index(drop=True)

    # Ensure all values are non-negative and reasonable
    dummy_df[feature_names] = dummy_df[feature_names].apply(lambda x: x.clip(lower=0))

    # Create processed directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    dummy_df.to_csv(output_path, index=False)

    # Log statistics
    logger.info(f"Dummy data generated with shape: {dummy_df.shape}")
    logger.info(f"Normal samples: {n_samples - n_anomalies}, Anomaly samples: {n_anomalies}")

    # Log some basic statistics
    logger.info(f"Label distribution: {dummy_df['Label'].value_counts().to_dict()}")
    logger.info(f"Feature value ranges (min-max):")
    for feature in feature_names[:5]:  # Show first 5 features as example
        min_val = dummy_df[feature].min()
        max_val = dummy_df[feature].max()
        logger.info(f"  {feature}: {min_val:.2f} - {max_val:.2f}")

    logger.info(f"Dummy data saved to {output_path}")
    logger.info("Dummy data generation completed.")

if __name__ == "__main__":
    generate_dummy_data()