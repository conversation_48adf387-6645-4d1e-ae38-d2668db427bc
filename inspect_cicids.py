import pandas as pd
import os
import glob
import numpy as np # <--- ADD THIS LINE

# Adjust this path if your 'data' folder is not directly inside ANMS_PROFECT
# For example, if you ran this script from ANMS_PROFECT/src, you might need '../data'
data_dir = './data'

# Find all CSV files in the data directory
csv_files = glob.glob(os.path.join(data_dir, '*.csv'))

if not csv_files:
    print(f"Error: No CSV files found in '{data_dir}'. Please ensure your CIC-IDS2017 files are in the correct 'data' folder.")
else:
    # Read the first CSV file found
    first_csv_file = csv_files[0]
    print(f"Inspecting file: {first_csv_file}")
    try:
        # Read a sample of 1000 rows to quickly get column names and types
        # Use low_memory=False to help pandas infer dtypes better across the file
        df_sample = pd.read_csv(first_csv_file, nrows=1000, low_memory=False)

        print("\n--- Columns in the dataset ---")
        print(df_sample.columns.tolist())

        print("\n--- Unique values and counts for 'Label' column (or last column if 'Label' not found) ---")
        # CIC-IDS2017 typically has 'Label' as the target column
        if 'Label' in df_sample.columns:
            print(df_sample['Label'].value_counts())
        else:
            print(f"Warning: 'Label' column not found. Assuming the last column '{df_sample.columns[-1]}' is the target.")
            print(df_sample[df_sample.columns[-1]].value_counts())

        print("\n--- Sample of data types (first 50 columns) ---")
        print(df_sample.dtypes.head(50)) # Print first 50 dtypes for brevity

        print("\n--- First 5 rows of the dataset ---")
        print(df_sample.head())

        # Check for NaN/Inf in the sample
        print("\n--- Checking for NaN/Infinity in sample ---")
        nan_cols = df_sample.columns[df_sample.isnull().any()].tolist()
        if nan_cols:
            print(f"Columns with NaNs: {nan_cols}")
            for col in nan_cols:
                print(f"  {col}: {df_sample[col].isnull().sum()} NaNs")
        else:
            print("No NaNs found in the sample rows.")

        # Check for infinities (often represented as np.inf)
        # Convert df_sample to numeric for this check, coercing errors to NaN
        df_numeric = df_sample.apply(pd.to_numeric, errors='coerce')
        inf_cols = df_numeric.columns[df_numeric.isin([pd.NA, np.inf, -np.inf]).any()].tolist()
        if inf_cols:
            print(f"Columns with Infinities: {inf_cols}")
            for col in inf_cols:
                print(f"  {col}: {(df_numeric[col] == np.inf).sum()} Inf, {(df_numeric[col] == -np.inf).sum()} -Inf")
        else:
            print("No Infinities found in the sample rows.")


    except Exception as e:
        print(f"An error occurred while reading the CSV: {e}")