#!/usr/bin/env python3

import pandas as pd
import numpy as np
import joblib
import logging
from src.utils.xai_explainer import XAI_Explainer

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_with_xai():
    print("=== Anomaly Detection with XAI Test ===")
    
    # Load models and data
    try:
        rf_model = joblib.load("models/supervised_model.pkl")
        iso_model = joblib.load("models/unsupervised_model.pkl")
        scaler = joblib.load("models/scaler.pkl")
        print("✓ Models loaded successfully")
        
        df = pd.read_csv("data/processed/dummy_cicids_data.csv")
        X = df.drop('Label', axis=1)
        y = df['Label']
        feature_names = X.columns.tolist()
        print(f"✓ Data loaded: {X.shape[0]} samples, {X.shape[1]} features")
        
    except Exception as e:
        print(f"✗ Error loading models/data: {e}")
        return
    
    # Initialize XAI explainer
    try:
        xai_explainer = XAI_Explainer(rf_model, feature_names)
        
        # Use normal training data as background for SHAP
        normal_data = X[y == 1].sample(100, random_state=42)  # Sample 100 normal instances
        xai_explainer.build_explainer(normal_data, target_class_index=0)  # Explain anomaly class (0)
        print("✓ XAI explainer initialized")
        
    except Exception as e:
        print(f"✗ Error initializing XAI: {e}")
        return
    
    # Test anomaly detection with explanations
    print("\n=== Testing Anomaly Detection with XAI ===")
    
    # Test on anomaly samples
    anomaly_samples = X[y == 0].head(3)
    print("\nAnomaly Samples with Explanations:")
    
    for i, (idx, sample) in enumerate(anomaly_samples.iterrows()):
        print(f"\n--- Anomaly Sample {i+1} ---")
        
        # Make predictions
        sample_df = pd.DataFrame([sample])
        sample_scaled = scaler.transform(sample_df)
        
        rf_pred = rf_model.predict(sample_df)[0]
        rf_proba = rf_model.predict_proba(sample_df)[0]
        
        iso_pred_raw = iso_model.predict(sample_scaled)[0]
        iso_pred = 0 if iso_pred_raw == -1 else 1
        iso_score = iso_model.decision_function(sample_scaled)[0]
        
        print(f"Predictions: RF={rf_pred} (prob_anom={rf_proba[0]:.3f}), ISO={iso_pred} (score={iso_score:.3f})")
        
        # Generate XAI explanation if anomaly detected
        if rf_pred == 0:  # If Random Forest detected anomaly
            try:
                explanation = xai_explainer.explain_instance(sample)
                if explanation:
                    top_features = xai_explainer.get_top_features(explanation, num_features=5)
                    print("XAI Explanation (Top 5 features contributing to anomaly detection):")
                    for j, (feature_name, shap_value) in enumerate(top_features):
                        direction = "increases" if shap_value > 0 else "decreases"
                        print(f"  {j+1}. {feature_name}: {shap_value:.4f} ({direction} anomaly probability)")
                        print(f"     Feature value: {sample[feature_name]:.2f}")
                else:
                    print("XAI Explanation: Failed to generate explanation")
            except Exception as e:
                print(f"XAI Explanation: Error - {e}")
        else:
            print("XAI Explanation: Not generated (no anomaly detected by RF)")
    
    # Test with extreme anomaly
    print(f"\n--- Extreme Anomaly Sample ---")
    extreme_sample = {}
    for feature in feature_names:
        if 'Packets' in feature:
            extreme_sample[feature] = 2000  # Very high
        elif 'Length' in feature or 'Bytes' in feature:
            extreme_sample[feature] = 100000  # Very large
        elif 'IAT' in feature:
            extreme_sample[feature] = 0.01  # Very short
        else:
            extreme_sample[feature] = 10000  # Generally high
    
    extreme_df = pd.DataFrame([extreme_sample])
    extreme_scaled = scaler.transform(extreme_df)
    
    rf_pred = rf_model.predict(extreme_df)[0]
    rf_proba = rf_model.predict_proba(extreme_df)[0]
    
    iso_pred_raw = iso_model.predict(extreme_scaled)[0]
    iso_pred = 0 if iso_pred_raw == -1 else 1
    iso_score = iso_model.decision_function(extreme_scaled)[0]
    
    print(f"Predictions: RF={rf_pred} (prob_anom={rf_proba[0]:.3f}), ISO={iso_pred} (score={iso_score:.3f})")
    
    if rf_pred == 0:
        try:
            extreme_series = pd.Series(extreme_sample)
            explanation = xai_explainer.explain_instance(extreme_series)
            if explanation:
                top_features = xai_explainer.get_top_features(explanation, num_features=5)
                print("XAI Explanation (Top 5 features contributing to anomaly detection):")
                for j, (feature_name, shap_value) in enumerate(top_features):
                    direction = "increases" if shap_value > 0 else "decreases"
                    print(f"  {j+1}. {feature_name}: {shap_value:.4f} ({direction} anomaly probability)")
                    print(f"     Feature value: {extreme_sample[feature_name]:.2f}")
            else:
                print("XAI Explanation: Failed to generate explanation")
        except Exception as e:
            print(f"XAI Explanation: Error - {e}")
    
    print("\n=== Test Complete ===")
    print("The system can now detect anomalies and provide explanations!")

if __name__ == "__main__":
    test_with_xai()
