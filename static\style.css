/* static/style.css */
body {
 font-family: Arial, sans-serif;
 margin: 0;
 padding: 0;
 background-color: #f4f7f6;
 color: #333;
}

header {
 background-color: #2c3e50;
 color: #ecf0f1;
 padding: 20px;
 text-align: center;
 box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
 font-size: 3.5em;
 font-weight: bold;
 color: whitesmoke;
 margin: 0;
}

#title {
 font-size: 1.5em;
 font-weight: bold;
 padding: 5px;
 margin-top: 0;
}

h1,
h2 {
 color: #2c3e50;
}

main {
 padding: 20px;
 max-width: 1200px;
 margin: 20px auto;
 background-color: #fff;
 border-radius: 8px;
 box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* style.css */


/* Container specifically for the network topology image */
.topology-container {
 width: 100%;
 /* Make the container take full width of its parent */
 max-width: none;
 /* Remove any maximum width constraint from the container */
 margin: 20px auto;
 /* Center the container and add vertical spacing */
 border: 1px solid #ccc;
 /* Add a border around the image container */
 padding: 10px;
 /* Add some padding inside the container */
 box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
 /* Add a subtle shadow */
 text-align: center;
 /* Center the image horizontally within this container */
}

/* The actual image styles */
.topology-container img {
 max-width: 100%;
 /* Ensures the image never overflows its container */
 height: auto;
 /* Maintains the image's aspect ratio */
 display: block;
 /* Removes extra space below the image */
 margin: 0 auto;
 /* Centers the image horizontally within its container */
 border: 1px solid #ddd;
 /* Optional: adds a thin border to the image itself */
}


.dashboard-section {
 margin-bottom: 30px;
 padding: 20px;
 background-color: #fdfdfd;
 border-radius: 8px;
 border: 1px solid #eee;
}

.kpi-grid {
 display: grid;
 grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
 gap: 20px;
 margin-top: 20px;
}

.kpi-card {
 background-color: #e0f2f7;
 padding: 15px;
 border-radius: 5px;
 text-align: center;
 box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.kpi-card h3 {
 margin-top: 0;
 color: #2980b9;
 font-size: 1.1em;
}

.kpi-card p {
 font-size: 1.8em;
 font-weight: bold;
 color: #34495e;
}

.network-topology {
 text-align: center;
 margin-top: 20px;
}

.log-container {
 max-height: 500px;
 overflow-y: auto;
 border: 1px solid #ddd;
 border-radius: 5px;
 margin-top: 15px;
}

table {
 width: 100%;
 border-collapse: collapse;
 font-size: 0.9em;
}

table th,
table td {
 border: 1px solid #ddd;
 padding: 8px;
 text-align: left;
}

table th {
 background-color: #f2f2f2;
 color: #555;
 position: sticky;
 top: 0;
 z-index: 1;
}

.anomaly-row {
 background-color: #ffe0e0;
}

.normal-row {
 background-color: #e6ffe6;
}

.status-anomaly {
 color: #e74c3c;
 font-weight: bold;
}

.status-normal {
 color: #2ecc71;
 font-weight: bold;
}

button {
 background-color: #2980b9;
 color: white;
 padding: 10px 15px;
 border: none;
 border-radius: 5px;
 cursor: pointer;
 font-size: 1em;
 margin-bottom: 10px;
}

button:hover {
 background-color: #3498db;
}

footer {
 text-align: center;
 padding: 20px;
 margin-top: 30px;
 background-color: #2c3e50;
 color: #ecf0f1;
 box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}