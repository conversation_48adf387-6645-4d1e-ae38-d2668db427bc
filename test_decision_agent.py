#!/usr/bin/env python3

import pandas as pd
import numpy as np
import logging
from decision_agent import DecisionAgent

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_decision_agent():
    """Test the decision agent with some sample data"""
    
    # Load the dummy data to get feature names
    data_path = "data/processed/dummy_cicids_data.csv"
    df = pd.read_csv(data_path)
    feature_names = [col for col in df.columns if col != 'Label']
    
    logger.info(f"Feature names: {feature_names}")
    
    # Initialize decision agent
    decision_agent = DecisionAgent(
        feature_names=feature_names,
        supervised_model_path="models/supervised_model.pkl",
        unsupervised_model_path="models/unsupervised_model.pkl",
        scaler_path="models/scaler.pkl"
    )
    
    # Test with some samples from the data
    X = df.drop('Label', axis=1)
    y = df['Label']
    
    # Test with a few normal samples
    logger.info("\n=== Testing Normal Samples ===")
    normal_samples = X[y == 1].head(3)
    for i, (idx, sample) in enumerate(normal_samples.iterrows()):
        sample_df = pd.DataFrame([sample], columns=feature_names)
        result = decision_agent.make_decision(sample_df)
        logger.info(f"Normal Sample {i+1}: Prediction={result['final_prediction']}, Is_Anomaly={result['is_anomaly']}")
        logger.info(f"  Supervised: {result['supervised_prediction']} (prob_anomaly: {result['supervised_proba_anomaly']:.3f})")
        logger.info(f"  Unsupervised: {result['unsupervised_prediction']} (score: {result['unsupervised_anomaly_score']:.3f})")
    
    # Test with a few anomaly samples
    logger.info("\n=== Testing Anomaly Samples ===")
    anomaly_samples = X[y == 0].head(3)
    for i, (idx, sample) in enumerate(anomaly_samples.iterrows()):
        sample_df = pd.DataFrame([sample], columns=feature_names)
        result = decision_agent.make_decision(sample_df)
        logger.info(f"Anomaly Sample {i+1}: Prediction={result['final_prediction']}, Is_Anomaly={result['is_anomaly']}")
        logger.info(f"  Supervised: {result['supervised_prediction']} (prob_anomaly: {result['supervised_proba_anomaly']:.3f})")
        logger.info(f"  Unsupervised: {result['unsupervised_prediction']} (score: {result['unsupervised_anomaly_score']:.3f})")
        if result['xai_explanation']:
            logger.info(f"  XAI Explanation: {result['xai_explanation']}")
    
    # Generate some extreme anomaly samples to test
    logger.info("\n=== Testing Extreme Anomaly Samples ===")
    for i in range(3):
        # Create extreme values that should definitely be anomalies
        extreme_sample = {}
        for feature in feature_names:
            if 'Packets' in feature:
                extreme_sample[feature] = np.random.uniform(500, 1000)  # Very high packet counts
            elif 'Length' in feature or 'Bytes' in feature:
                extreme_sample[feature] = np.random.uniform(10000, 20000)  # Very large sizes
            elif 'IAT' in feature:
                extreme_sample[feature] = np.random.uniform(0.1, 1)  # Very short IATs
            else:
                extreme_sample[feature] = np.random.uniform(1000, 2000)  # Generally high values
        
        sample_df = pd.DataFrame([extreme_sample], columns=feature_names)
        result = decision_agent.make_decision(sample_df)
        logger.info(f"Extreme Sample {i+1}: Prediction={result['final_prediction']}, Is_Anomaly={result['is_anomaly']}")
        logger.info(f"  Supervised: {result['supervised_prediction']} (prob_anomaly: {result['supervised_proba_anomaly']:.3f})")
        logger.info(f"  Unsupervised: {result['unsupervised_prediction']} (score: {result['unsupervised_anomaly_score']:.3f})")
        if result['xai_explanation']:
            logger.info(f"  XAI Explanation: {result['xai_explanation']}")

if __name__ == "__main__":
    test_decision_agent()
