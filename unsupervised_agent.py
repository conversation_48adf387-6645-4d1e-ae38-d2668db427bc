# unsupervised_agent.py
import joblib
import logging
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class UnsupervisedAgent:
    def __init__(self, model_path="models/unsupervised_model.pkl", scaler_path="models/scaler.pkl"):
        """
        Loads the trained unsupervised model and scaler.
        """
        self.model = self._load_model(model_path)
        self.scaler = self._load_model(scaler_path)
        logger.info("UnsupervisedAgent initialized.")

    def _load_model(self, model_path):
        """Loads a pre-trained model or scaler."""
        try:
            model = joblib.load(model_path)
            logger.info(f"Unsupervised model/scaler loaded from {model_path}")
            return model
        except FileNotFoundError:
            logger.error(f"Unsupervised model/scaler not found at {model_path}. Please train it first.")
            return None

    def predict(self, data):
        """
        Makes predictions using the unsupervised model.
        Args:
            data (pd.DataFrame or np.array): Input features.
        Returns:
            np.array: Predicted labels (-1 for anomaly, 1 for normal).
        """
        if self.model is None or self.scaler is None:
            logger.error("Model or scaler not loaded. Cannot predict.")
            return np.array([])

        if isinstance(data, np.ndarray):
            # Ensure consistent column names if converting from numpy
            data_df = pd.DataFrame(data, columns=[f'feature_{i}' for i in range(data.shape[1])])
        else:
            data_df = data.copy()

        scaled_data = self.scaler.transform(data_df)
        scaled_data_df = pd.DataFrame(scaled_data, columns=data_df.columns) # Retain feature names
        return self.model.predict(scaled_data_df)

    def decision_function(self, data):
        """
        Gets decision scores from the unsupervised model.
        Lower scores indicate higher anomaly likelihood.
        Args:
            data (pd.DataFrame or np.array): Input features.
        Returns:
            np.array: Decision scores.
        """
        if self.model is None or self.scaler is None:
            logger.error("Model or scaler not loaded. Cannot get decision function.")
            return np.array([])

        if isinstance(data, np.ndarray):
            data_df = pd.DataFrame(data, columns=[f'feature_{i}' for i in range(data.shape[1])])
        else:
            data_df = data.copy()

        scaled_data = self.scaler.transform(data_df)
        scaled_data_df = pd.DataFrame(scaled_data, columns=data_df.columns)
        return self.model.decision_function(scaled_data_df)