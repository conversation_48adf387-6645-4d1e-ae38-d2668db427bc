import dash
from dash import dcc, html, dash_table
from dash.dependencies import Input, Output
import plotly.graph_objs as go
import sqlite3
import pandas as pd
import networkx as nx
from datetime import datetime, timedelta
import os
import random

# --- Database Path ---
DB_PATH = "logs/anms_log.db"

# --- Network Topology Definition ---
# This dictionary defines the nodes and their properties (position, label, type, zone)
# Greatly expanded positions for a much wider, more spread-out view
nodes_info = {
    # --- INTERNET / WAN EDGE (top-left) ---
    'internet_cloud':       {'pos': (-40, 40), 'label': 'Internet Cloud', 'type': 'external', 'zone': 'Internet'},
    'isp_router':           {'pos': (-30, 40), 'label': 'ISP Router', 'type': 'router', 'zone': 'Internet'},
    'campus_border_router': {'pos': (-20, 40), 'label': 'Campus Border Router', 'type': 'router', 'zone': 'Perimeter'},
    'external_dns':         {'pos': (-35, 43), 'label': 'External DNS', 'type': 'server', 'zone': 'Internet'},

    # --- CAMPUS CORE (central top) ---
    'main_firewall':        {'pos': (-10, 38), 'label': 'Main Firewall', 'type': 'security', 'zone': 'Core'},
    'core_router_1':        {'pos': (-5, 35), 'label': 'Core Router 1', 'type': 'router', 'zone': 'Core'},
    'core_router_2':        {'pos': (5, 35), 'label': 'Core Router 2', 'type': 'router', 'zone': 'Core'},
    'dns_server_campus':    {'pos': (-7, 30), 'label': 'Campus DNS', 'type': 'server', 'zone': 'Core'},
    'dhcp_server_campus':   {'pos': (7, 30), 'label': 'Campus DHCP', 'type': 'server', 'zone': 'Core'},

    # --- DMZ (right of core) ---
    'dmz_firewall':         {'pos': (20, 38), 'label': 'DMZ Firewall', 'type': 'security', 'zone': 'DMZ'},
    'web_server_prod':      {'pos': (20, 32), 'label': 'Prod Web Svr', 'type': 'server', 'zone': 'DMZ'},
    'student_portal_server':{'pos': (25, 30), 'label': 'Student Portal', 'type': 'server', 'zone': 'DMZ'},
    'email_server':         {'pos': (15, 30), 'label': 'Email Server', 'type': 'server', 'zone': 'DMZ'},

    # --- ACADEMIC BUILDING A (left side) ---
    'building_a_l3_switch': {'pos': (-25, 20), 'label': 'Bldg A L3 Switch', 'type': 'switch', 'zone': 'Bldg_A'},
    'cs_lab_switch':        {'pos': (-30, 15), 'label': 'CS Lab Switch', 'type': 'switch', 'zone': 'Bldg_A'},
    'cs_lab_pc_1':          {'pos': (-33, 10), 'label': 'CS Lab PC 1', 'type': 'workstation', 'zone': 'Bldg_A'},
    'cs_lab_pc_2':          {'pos': (-28, 10), 'label': 'CS Lab PC 2', 'type': 'workstation', 'zone': 'Bldg_A'},
    'cs_prof_office_switch':{'pos': (-20, 15), 'label': 'Prof Office Switch', 'type': 'switch', 'zone': 'Bldg_A'},
    'cs_prof_pc_1':         {'pos': (-23, 10), 'label': 'Prof PC 1', 'type': 'workstation', 'zone': 'Bldg_A'},
    'cs_prof_laptop_1':     {'pos': (-18, 10), 'label': 'Prof Laptop 1', 'type': 'workstation', 'zone': 'Bldg_A'},
    'cs_printer':           {'pos': (-25, 5), 'label': 'CS Printer', 'type': 'iot', 'zone': 'Bldg_A'},
    'cs_ap_1':              {'pos': (-20, 22), 'label': 'CS AP 1', 'type': 'wireless_ap', 'zone': 'Bldg_A'},
    'student_phone_1':      {'pos': (-28, 25), 'label': 'Student Phone', 'type': 'mobile', 'zone': 'Bldg_A'},
    'prof_tablet_1':        {'pos': (-22, 25), 'label': 'Prof Tablet', 'type': 'mobile', 'zone': 'Bldg_A'},

    # --- ADMINISTRATION BUILDING B (center-right) ---
    'building_b_l3_switch': {'pos': (10, 20), 'label': 'Bldg B L3 Switch', 'type': 'switch', 'zone': 'Bldg_B'},
    'admin_office_switch':  {'pos': (5, 15), 'label': 'Admin Office Switch', 'type': 'switch', 'zone': 'Bldg_B'},
    'hr_workstation':       {'pos': (3, 10), 'label': 'HR Workstation', 'type': 'workstation', 'zone': 'Bldg_B'},
    'finance_workstation':  {'pos': (8, 10), 'label': 'Finance Workstation', 'type': 'workstation', 'zone': 'Bldg_B'},
    'building_b_ap_1':      {'pos': (12, 22), 'label': 'Bldg B AP 1', 'type': 'wireless_ap', 'zone': 'Bldg_B'},
    'guest_laptop_1':       {'pos': (8, 25), 'label': 'Guest Laptop', 'type': 'mobile', 'zone': 'Bldg_B'},
    'ip_phone_admin':       {'pos': (10, 10), 'label': 'Admin IP Phone', 'type': 'iot', 'zone': 'Bldg_B'},

    # --- RESEARCH LAB (Isolated Network, bottom-right) ---
    'research_router':      {'pos': (30, 15), 'label': 'Research Router', 'type': 'router', 'zone': 'Research_Lab'},
    'research_firewall':    {'pos': (30, 10), 'label': 'Research Firewall', 'type': 'security', 'zone': 'Research_Lab'},
    'hpc_cluster_head':     {'pos': (25, 5), 'label': 'HPC Cluster Head', 'type': 'server', 'zone': 'Research_Lab'},
    'data_storage_server':  {'pos': (35, 5), 'label': 'Data Storage Svr', 'type': 'server', 'zone': 'Research_Lab'},
    'lab_instrument_1':     {'pos': (28, 0), 'label': 'Instrument 1', 'type': 'iot', 'zone': 'Research_Lab'},
    'research_pc_1':        {'pos': (32, 0), 'label': 'Research PC 1', 'type': 'workstation', 'zone': 'Research_Lab'},
    'research_ap_1':        {'pos': (35, 12), 'label': 'Research AP 1', 'type': 'wireless_ap', 'zone': 'Research_Lab'},
    'researcher_tablet':    {'pos': (38, 8), 'label': 'Researcher Tablet', 'type': 'mobile', 'zone': 'Research_Lab'},

    # --- ANMS Agents (near Core) ---
    'monitoring_agent_core':    {'pos': (-15, 30), 'label': 'Monitoring Agent (Core)', 'type': 'agent', 'zone': 'Core'},
    'sda_agent':                {'pos': (-10, 25), 'label': 'SDA Agent', 'type': 'agent', 'zone': 'Core'},
    'uaa_agent':                {'pos': (0, 25), 'label': 'UAA Agent', 'type': 'agent', 'zone': 'Core'},
    'decision_agent':           {'pos': (-5, 20), 'label': 'Decision Agent', 'type': 'agent', 'zone': 'Core'},
    'log_analyzer_agent':       {'pos': (15, 20), 'label': 'Log Analyzer Agent', 'type': 'agent', 'zone': 'Core'},

    # Fallback for unknown IPs (can be placed off-grid or strategically)
    'unknown':              {'pos': (45, -5), 'label': 'Unknown Device', 'type': 'unknown', 'zone': 'N/A'}
}


# --- Node Type Styling ---
node_type_styles = {
    'router': {'color': 'rgba(100, 100, 255, 0.8)', 'symbol': 'square', 'size': 25, 'line_width': 2},
    'switch': {'color': 'rgba(120, 180, 120, 0.8)', 'symbol': 'triangle-up', 'size': 22, 'line_width': 1.5},
    'server': {'color': 'rgba(255, 150, 100, 0.8)', 'symbol': 'diamond', 'size': 28, 'line_width': 2.5},
    'workstation': {'color': 'rgba(150, 150, 255, 0.8)', 'symbol': 'circle', 'size': 18, 'line_width': 1},
    'mobile': {'color': 'rgba(180, 120, 255, 0.8)', 'symbol': 'star', 'size': 20, 'line_width': 1.2},
    'iot': {'color': 'rgba(255, 100, 100, 0.8)', 'symbol': 'hexagram', 'size': 15, 'line_width': 0.8},
    'security': {'color': 'rgba(255, 200, 50, 0.8)', 'symbol': 'pentagon', 'size': 30, 'line_width': 3},
    'external': {'color': 'rgba(200, 200, 200, 0.8)', 'symbol': 'x', 'size': 35, 'line_width': 3.5},
    'agent': {'color': 'rgba(50, 200, 200, 0.8)', 'symbol': 'star-diamond', 'size': 25, 'line_width': 2},
    'wireless_ap': {'color': 'rgba(200, 100, 200, 0.8)', 'symbol': 'circle-open', 'size': 20, 'line_width': 1.5},
    'unknown': {'color': 'rgba(255, 0, 0, 0.5)', 'symbol': 'x', 'size': 15, 'line_width': 0.5}
}

# --- Network Zone Definitions for Background Rectangles ---
# Each tuple is (x0, y0, x1, y1, color, name)
network_zones = [
    # Internet/WAN Edge
    (-45, 38, -15, 45, 'rgba(180, 180, 200, 0.1)', 'Internet'),
    # Perimeter
    (-25, 37, -15, 42, 'rgba(200, 200, 150, 0.1)', 'Perimeter'),
    # Core Network
    (-18, 28, 18, 40, 'rgba(150, 200, 150, 0.1)', 'Core Network'),
    # DMZ
    (13, 28, 28, 40, 'rgba(200, 150, 150, 0.1)', 'DMZ'),
    # Academic Building A
    (-35, 0, -15, 28, 'rgba(150, 150, 200, 0.1)', 'Academic Building A'),
    # Administration Building B
    (0, 0, 15, 28, 'rgba(200, 180, 150, 0.1)', 'Admin Building B'),
    # Research Lab
    (22, -5, 40, 20, 'rgba(150, 200, 200, 0.1)', 'Research Lab'),
]


# --- Connectivity (Simplified for visualization) ---
edges = [
    ('internet_cloud', 'isp_router'),
    ('isp_router', 'campus_border_router'),
    ('campus_border_router', 'main_firewall'),
    ('main_firewall', 'core_router_1'),
    ('main_firewall', 'core_router_2'),
    ('core_router_1', 'dns_server_campus'),
    ('core_router_1', 'dhcp_server_campus'),
    ('core_router_2', 'dns_server_campus'),
    ('core_router_2', 'dhcp_server_campus'),
    ('core_router_1', 'building_a_l3_switch'),
    ('core_router_2', 'building_b_l3_switch'),
    ('main_firewall', 'dmz_firewall'),
    ('dmz_firewall', 'web_server_prod'),
    ('dmz_firewall', 'student_portal_server'),
    ('dmz_firewall', 'email_server'),
    ('building_a_l3_switch', 'cs_lab_switch'),
    ('building_a_l3_switch', 'cs_prof_office_switch'),
    ('building_a_l3_switch', 'cs_ap_1'),
    ('cs_lab_switch', 'cs_lab_pc_1'),
    ('cs_lab_switch', 'cs_lab_pc_2'),
    ('cs_lab_switch', 'cs_printer'),
    ('cs_prof_office_switch', 'cs_prof_pc_1'),
    ('cs_prof_office_switch', 'cs_prof_laptop_1'),
    ('cs_ap_1', 'student_phone_1'),
    ('cs_ap_1', 'prof_tablet_1'),
    ('building_b_l3_switch', 'admin_office_switch'),
    ('building_b_l3_switch', 'building_b_ap_1'),
    ('admin_office_switch', 'hr_workstation'),
    ('admin_office_switch', 'finance_workstation'),
    ('admin_office_switch', 'ip_phone_admin'),
    ('building_b_ap_1', 'guest_laptop_1'),
    ('campus_border_router', 'research_router'), # Research lab is somewhat isolated
    ('research_router', 'research_firewall'),
    ('research_firewall', 'hpc_cluster_head'),
    ('research_firewall', 'data_storage_server'),
    ('research_firewall', 'lab_instrument_1'),
    ('research_firewall', 'research_pc_1'),
    ('research_router', 'research_ap_1'),
    ('research_ap_1', 'researcher_tablet'),
    # Connections to Agents (conceptual)
    ('core_router_1', 'monitoring_agent_core'),
    ('monitoring_agent_core', 'sda_agent'),
    ('monitoring_agent_core', 'uaa_agent'),
    ('sda_agent', 'decision_agent'),
    ('uaa_agent', 'decision_agent'),
    ('decision_agent', 'log_analyzer_agent'),
    ('dmz_firewall', 'log_analyzer_agent'),
    ('main_firewall', 'log_analyzer_agent')
]

# --- Helper Functions ---
def get_node_style(node_type, is_highlighted=False):
    """Returns style properties for a node based on its type and highlight status."""
    style = node_type_styles.get(node_type, node_type_styles['unknown']) # Fallback to 'unknown' style if type is truly unexpected

    # Base styling
    color = style['color']
    line_color = 'rgba(0,0,0,0.8)' # Default black border
    line_width = style['line_width']

    # Highlight style for suspicious nodes
    if is_highlighted:
        color = 'rgba(255, 215, 0, 0.9)' # Gold fill for malicious
        line_color = 'red' # Red border for malicious
        line_width = 3.5 # Thicker red border

    return {
        'marker': {
            'symbol': style['symbol'],
            'size': style['size'],
            'color': color,
            'line': {'width': line_width, 'color': line_color}
        },
        'hoverinfo': 'text' # Will be set by node_text in update_graph_and_table
    }

def map_ip_to_node_id(ip_address_str):
    """Maps an IP address back to a conceptual node ID in the dashboard topology."""
    # Explicit IP to Node ID mapping - more comprehensive for realistic simulation
    ip_to_node_map = {
        # Academic Building A
        '************': 'cs_lab_pc_1', # Example specific IP
        '************': 'cs_lab_pc_2',
        '************': 'cs_lab_pc_1', # Can map multiple IPs to one node for simplicity
        '************': 'cs_prof_pc_1',
        '************': 'cs_prof_laptop_1',
        '*************': 'cs_printer',
        '************': 'student_phone_1',
        '************': 'prof_tablet_1',
        # Administration Building B
        '************': 'hr_workstation',
        '************': 'finance_workstation',
        '************': 'guest_laptop_1',
        '************0': 'ip_phone_admin',
        # Research Lab
        '************': 'hpc_cluster_head',
        '************': 'data_storage_server',
        '*************': 'lab_instrument_1',
        '************': 'research_pc_1',
        '************': 'researcher_tablet',
        # DMZ
        '*********': 'web_server_prod',
        '*********': 'student_portal_server',
        '*********': 'email_server',
        # Core/Perimeter
        '**********': 'campus_border_router',
        '**********0': 'main_firewall',
        '***********': 'dns_server_campus',
        '***********': 'dhcp_server_campus',
        '***********': 'core_router_1',
        '***********': 'core_router_2',
        '***********': 'monitoring_agent_core',
        '***********': 'sda_agent',
        '***********': 'uaa_agent',
        '***********': 'decision_agent',
        '***********': 'log_analyzer_agent',
        # External/Internet
        '*******': 'external_dns',
        '***********': 'isp_router',
        '*******': 'external_dns', # another common DNS
        '************/19': 'internet_cloud', # Google IP range example
        '************/24': 'internet_cloud', # Example external network
        '127.0.0.1': 'monitoring_agent_core', # Localhost might represent the agent itself
        '0.0.0.0': 'internet_cloud' # Represent inbound from internet
    }

    # Try direct mapping first
    if ip_address_str in ip_to_node_map:
        return ip_to_node_map[ip_address_str]

    # Fallback to broader range mapping for dynamically generated IPs
    # The '192.168.1.X' range will now map to cs_lab_pc_1 for any X, and so on.
    if ip_address_str.startswith('192.168.1.'): return 'cs_lab_pc_1'
    if ip_address_str.startswith('192.168.2.'): return 'cs_prof_pc_1'
    if ip_address_str.startswith('192.168.3.'): return 'student_phone_1'
    if ip_address_str.startswith('192.168.4.'): return 'hr_workstation'
    if ip_address_str.startswith('192.168.5.'): return 'guest_laptop_1'
    if ip_address_str.startswith('192.168.6.'): return 'research_pc_1'
    if ip_address_str.startswith('192.168.7.'): return 'researcher_tablet'
    if ip_address_str.startswith('10.0.0.'): return 'web_server_prod'
    if ip_address_str.startswith('172.16.0.'): return 'core_router_1'
    if ip_address_str.startswith('203.0.113.'): return 'isp_router'
    if ip_address_str.startswith('8.8.8.'): return 'external_dns' # For general DNS queries
    if ip_address_str.startswith('1.1.1.'): return 'external_dns' # For general DNS queries

    # Check for broader external ranges (CIDR-like)
    # This is a simple starts-with check, not full CIDR logic.
    if ip_address_str.startswith('64.233.') or ip_address_str.startswith('198.51.'):
        return 'internet_cloud'

    return 'unknown' # Default if no specific or range mapping found

def display_recent_decisions(db_path, limit=50):
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        df = pd.read_sql_query(f"""
            SELECT timestamp, flow_id, source_ip, dest_ip, source_port, dest_port, sda_result, uaa_result, final_decision, action, log_message
            FROM decisions
            ORDER BY id DESC
            LIMIT {limit}
        """, conn)
        # Robust timestamp conversion
        df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce').dt.strftime('%H:%M:%S').fillna('N/A')
        return df
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return pd.DataFrame() # Return empty DataFrame on error
    finally:
        if conn:
            conn.close()

# --- Initialize Dash App ---
app = dash.Dash(__name__)
app.title = "ANMS Dashboard"

# --- App Layout ---
# --- App Layout ---
app.layout = html.Div(style={'fontFamily': 'Arial, sans-serif', 'backgroundColor': '#eef2f7', 'padding': '20px'}, children=[
    html.H1("University Network Anomaly Monitoring System", style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '20px', 'fontSize': '3em'}),

    # Network Topology Section (wider and takes more space)
    html.Div([ # This is the opening bracket for the children of THIS html.Div
        html.H2("Live Network Topology", style={'textAlign': 'center', 'color': '#34495e', 'marginBottom': '15px', 'fontSize': '2em'}),
        dcc.Graph(id='network-graph', style={'height': '800px', 'width': '100%'}, config={'displayModeBar': True})
    ], style={ # <--- This closing bracket ']' for children, and then the style, should be part of the same html.Div call
        'width': '95%',
        'margin': '0 auto 30px auto',
        'padding': '20px',
        'backgroundColor': 'white',
        'borderRadius': '15px'
    }), # Full width banner-like

    # Recent Detections Table Section (below the graph)
    html.Div([ # This is a new html.Div, so its children list starts here
        html.H2("Recent Detections & Decisions", style={'textAlign': 'center', 'color': '#34495e', 'marginBottom': '15px', 'fontSize': '2em'}),
        dash_table.DataTable(
            id='decisions-table',
            columns=[
                {'name': 'Time', 'id': 'timestamp', 'type': 'text'},
                {'name': 'Flow ID', 'id': 'flow_id', 'type': 'numeric'},
                {'name': 'Source IP', 'id': 'source_ip', 'type': 'text'},
                {'name': 'Dest IP', 'id': 'dest_ip', 'type': 'text'},
                {'name': 'Src Port', 'id': 'source_port', 'type': 'numeric'},
                {'name': 'Dst Port', 'id': 'dest_port', 'type': 'numeric'},
                {'name': 'SDA Result', 'id': 'sda_result', 'type': 'text'},
                {'name': 'UAA Result', 'id': 'uaa_result', 'type': 'text'},
                {'name': 'Decision', 'id': 'final_decision', 'type': 'text'},
                {'name': 'Action', 'id': 'action', 'type': 'text'},
                {'name': 'Message', 'id': 'log_message', 'type': 'text'}
            ],
            data=[],
            style_table={'overflowX': 'auto', 'minWidth': '100%', 'height': '450px', 'overflowY': 'auto', 'marginBottom': '15px', 'borderCollapse': 'collapse'},
            style_header={
                'backgroundColor': '#dce8f5',
                'fontWeight': 'bold',
                'borderBottom': '3px solid #aabedc',
                'padding': '12px',
                'textAlign': 'center',
                'fontSize': '14px',
                'color': '#333'
            },
            style_cell={
                'textAlign': 'left',
                'padding': '10px',
                'fontFamily': 'Arial, sans-serif',
                'fontSize': '13px',
                'border': '1px solid #e5e5e5',
                'whiteSpace': 'normal',
                'height': 'auto',
                'minWidth': '80px', 'width': 'auto', 'maxWidth': '200px', # Added responsive widths
            },
            style_data_conditional=[
                {
                    'if': {'filter_query': '{final_decision} contains "Malicious"'},
                    'backgroundColor': '#fff3e0', # Lighter orange for malicious
                    'color': '#d35400', # Darker orange text
                    'fontWeight': 'bold'
                },
                {
                    'if': {'filter_query': '{final_decision} contains "Anomaly"'},
                    'backgroundColor': '#e0f2f7', # Light cyan for anomaly
                    'color': '#2196f3', # Blue text
                },
                {
                    'if': {'filter_query': '{action} contains "Block"'},
                    'backgroundColor': '#ffdddd', # Light red for Block action
                    'color': '#c0392b', # Dark red text
                    'fontWeight': 'bold'
                },
                {
                    'if': {'row_index': 'odd'}, # Striping for readability
                    'backgroundColor': '#f9f9f9'
                }
            ],
            page_size=10, # Number of rows per page
            sort_action="native",
            filter_action="native",
        )
    ], style={'width': '95%', 'margin': '0 auto', 'padding': '20px', 'backgroundColor': 'white', 'borderRadius': '15px', 'boxShadow': '5px 5px 20px rgba(0,0,0,0.15)'}), # Full width for table too

    # Interval component for live updates
    dcc.Interval(
        id='interval-component',
        interval=5*1000, # in milliseconds (5 seconds)
        n_intervals=0
    )
]) # <--- This closes the outermost html.Div


@app.callback(
    Output('network-graph', 'figure'),
    Output('decisions-table', 'data'),
    Input('interval-component', 'n_intervals')
)
def update_graph_and_table(n):
    try:
        # Fetch recent decisions
        df_decisions = display_recent_decisions(DB_PATH, limit=50)

        # Initialize graph
        G = nx.Graph()
        for node_id, info in nodes_info.items():
            G.add_node(node_id, pos=info['pos'], label=info['label'], type=info['type'], zone=info['zone'])

        # Set of all valid node IDs from nodes_info for quick lookup
        valid_node_ids = set(nodes_info.keys())

        for u, v in edges:
            # Add edge ONLY if both 'u' and 'v' are valid node IDs defined in nodes_info
            if u in valid_node_ids and v in valid_node_ids:
                G.add_edge(u, v)
            else:
                # Print a warning if an edge points to an undefined node
                if u not in valid_node_ids:
                    print(f"Warning: Node '{u}' in edges list is not defined in nodes_info.")
                if v not in valid_node_ids:
                    print(f"Warning: Node '{v}' in edges list is not defined in nodes_info.")

        pos = nx.get_node_attributes(G, 'pos')

        edge_x = []
        edge_y = []
        for edge in G.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.append(x0)
            edge_x.append(x1)
            edge_x.append(None)
            edge_y.append(y0)
            edge_y.append(y1)
            edge_y.append(None)

        edge_trace = go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=0.8, color='#888'), # Slightly thicker edges
            hoverinfo='none',
            mode='lines')

        node_x = []
        node_y = []
        node_text = []
        node_styles = []

        # Prepare the list of current detection statuses for hover info
        current_detections_hover = {node_id: 'None' for node_id in nodes_info.keys()}
        # NOTE: For this to work accurately, you need to ensure your nodes_info
        # contains an 'ip_address' field for each node, and main.py generates
        # IPs that match these. We will address this in a later step.
        # For now, it will default to 'None'.
        
        # You need to convert df_decisions to a list of dicts to iterate if it's not already
        table_data = df_decisions.to_dict('records')

        for entry in table_data:
            src_ip = entry.get('source_ip')
            dst_ip = entry.get('dest_ip')
            
            source_node = map_ip_to_node_id(src_ip)
            dest_node = map_ip_to_node_id(dst_ip)
            
            detection_info = f"SDA: {entry.get('sda_result')}, UAA: {entry.get('uaa_result')}, Action: {entry.get('action')}"
            
            if source_node in nodes_info:
                current_detections_hover[source_node] = detection_info
            if dest_node in nodes_info and dest_node != source_node:
                current_detections_hover[dest_node] = detection_info


        # Track nodes involved in recent malicious activity/anomalies
        highlight_nodes = set()
        if not df_decisions.empty:
            malicious_flows = df_decisions[df_decisions['final_decision'].isin(['Malicious Activity Detected', 'Anomaly Detected'])]
            for _, row in malicious_flows.iterrows():
                src_node = map_ip_to_node_id(row['source_ip'])
                dst_node = map_ip_to_node_id(row['dest_ip'])

                # Add to highlight_nodes only if they are valid nodes in our graph definition
                # Ensure the node_id returned by map_ip_to_node_id actually exists in nodes_info
                if src_node in nodes_info:
                    highlight_nodes.add(src_node)
                if dst_node in nodes_info:
                    highlight_nodes.add(dst_node)

        for node_id in G.nodes():
            x, y = pos[node_id]
            node_x.append(x)
            node_y.append(y)

            # Enhanced Hover Text
            info = nodes_info.get(node_id, {})
            label = info.get('label', node_id)

            # Safely get node_type, defaulting to 'unknown' if not found or malformed
            # Ensure node_type is always a string that can be used as a key
            node_type = info.get('type')
            if node_type not in node_type_styles:
                node_type = 'unknown'

            zone = info.get('zone', 'N/A')
            
            # The text displayed on the graph (just the label)
            node_text.append(label)

            is_highlighted = node_id in highlight_nodes

            node_styles.append(get_node_style(node_type, is_highlighted)['marker'])


        node_trace = go.Scatter( # <--- THIS IS THE ONLY node_trace DEFINITION THAT SHOULD REMAIN
            x=node_x,
            y=node_y,
            mode='markers+text', # Keep text visible on the graph
            text=node_text, # Use the simplified node_text for on-graph labels
            textposition="bottom center",
            hoverinfo='text', # Crucial: tells Plotly to use 'hovertext' property for tooltip
            hovertext=[
                f"<b>{info.get('label', node_id)}</b><br>"
                f"Type: {info.get('type', 'unknown').replace('_', ' ').title()}<br>"
                f"Zone: {info.get('zone', 'N/A')}<br>"
                f"Current Detections: {current_detections_hover.get(node_id, 'None')}"
                for node_id, info in nodes_info.items()
            ], # This content appears only on hover
            marker=go.scatter.Marker(
                color=[style['color'] for style in node_styles],
                size=[style['size'] for style in node_styles],
                symbol=[style['symbol'] for style in node_styles],
                line=dict(
                    width=[style['line']['width'] for style in node_styles],
                    color=[style['line']['color'] for style in node_styles]
                )
            )
        )

        # Define background shapes for zones
        shapes = []
        annotations = []
        for x0, y0, x1, y1, color, name in network_zones:
            shapes.append(
                go.layout.Shape(
                    type="rect",
                    xref="x", yref="y",
                    x0=x0, y0=y0, x1=x1, y1=y1,
                    fillcolor=color,
                    line_width=0,
                    opacity=0.5,
                    layer="below"
                )
            )
            annotations.append(
                go.layout.Annotation(
                    x=(x0 + x1) / 2,
                    y=(y0 + y1) / 2,
                    xref="x", yref="y",
                    text=f"<b>{name}</b>",
                    showarrow=False,
                    font=dict(size=16, color='rgba(50,50,50,0.5)'),
                    opacity=0.6,
                    align="center",
                    valign="middle",
                    xanchor="center",
                    yanchor="middle",
                    textangle=0
                )
            )

        fig = go.Figure(data=[edge_trace, node_trace],
                    layout=go.Layout(
                        title=dict(text='<br>University Network Topology (Live Detections)', font=dict(size=24, color='#2c3e50')),
                        showlegend=False,
                        hovermode='closest',
                        margin=dict(b=20,l=5,r=5,t=40),
                        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[-60, 60]),
                        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[-20, 60]),
                        plot_bgcolor='#fcfcfc',
                        height=900,
                        width=1200,
                        shapes=shapes,
                        annotations=annotations,
                    )
                )

        return fig, df_decisions.to_dict('records')

    except Exception as e:
        print(f"Error in update_graph_and_table callback: {e}")
        import traceback
        traceback.print_exc()
        return go.Figure(), []

if __name__ == '__main__':
    app.run(debug=True)