# train_models.py
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import joblib # For saving models
from collections import Counter
import logging
import os # Import os for path operations

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def train_and_save_models(data_path="data/processed/cicids2017_processed.csv", models_dir="models/"):
    """
    Loads processed CICIDS2017 data, trains Supervised (RandomForest) and Unsupervised (IsolationForest) models,
    and saves them.
    """
    logger.info(f"Loading data from {data_path}")
    try:
        # Assuming your processed data has 'Label' as the target column
        df = pd.read_csv(data_path)
        # Infer anomaly_percentage from actual data
        # Assuming Label 0 is anomaly and Label 1 is normal
        total_samples = len(df)
        anomaly_count = df[df['Label'] == 0].shape[0]
        # Set a minimum contamination, as Isolation Forest can struggle with very low values
        # This is a hyperparameter you might tune later
        anomaly_percentage = max(0.001, anomaly_count / total_samples) # At least 0.1% contamination
        logger.info(f"Inferred anomaly percentage from data: {anomaly_percentage:.4f}")

    except FileNotFoundError:
        logger.error(f"Error: Data file not found at {data_path}. Please ensure it exists.")
        logger.warning("Generating dummy data for demonstration purposes as real file not found.")
        num_samples = 5000
        num_features = 60
        df = pd.DataFrame(np.random.rand(num_samples, num_features), columns=[f'feature_{i}' for i in range(num_features)])
        df['Label'] = 1 # Majority class is 1 (Normal)
        dummy_anomaly_percentage = 0.05 # 5% anomalies for dummy data
        num_anomalies = int(num_samples * dummy_anomaly_percentage)
        anomaly_indices = np.random.choice(num_samples, num_anomalies, replace=False)
        df.loc[anomaly_indices, 'Label'] = 0 # Minority class is 0 (Anomaly)
        logger.warning(f"Dummy data generated with shape {df.shape} and class distribution: {Counter(df['Label'])}")
        anomaly_percentage = dummy_anomaly_percentage


    # Separate features and target
    X = df.drop('Label', axis=1)
    y = df['Label']

    # Important: Ensure consistent feature names for XAI
    feature_names = X.columns.tolist()

    logger.info(f"Original class distribution: {Counter(y)}")

    # Split data for supervised learning
    X_train_sup, X_test_sup, y_train_sup, y_test_sup = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    logger.info(f"Supervised training set class distribution: {Counter(y_train_sup)}")

    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_sup)
    X_test_scaled = scaler.transform(X_test_sup)

    # Convert scaled numpy arrays back to DataFrame, ensuring default integer index
    X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=feature_names)
    X_test_scaled_df = pd.DataFrame(X_test_scaled, columns=feature_names)

    # Reset index of y_train_sup so it aligns with X_train_scaled_df's default index
    # This is CRUCIAL for the Isolation Forest training
    y_train_sup_reset = y_train_sup.reset_index(drop=True)


    # --- Train Supervised Agent (RandomForest) ---
    logger.info("Training Supervised Agent (RandomForestClassifier)...")
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced', n_jobs=-1)
    rf_model.fit(X_train_sup, y_train_sup) # Use original X_train_sup and y_train_sup

    # Evaluation for Supervised Model
    y_pred_sup = rf_model.predict(X_test_sup)
    logger.info("Supervised Model (RandomForest) Evaluation:")
    logger.info(f"\nConfusion Matrix:\n{confusion_matrix(y_test_sup, y_pred_sup)}")
    logger.info(f"\nClassification Report:\n{classification_report(y_test_sup, y_pred_sup, target_names=['Anomaly (0)', 'Normal (1)'])}")


    # --- Train Unsupervised Agent (IsolationForest) ---
    logger.info("Training Unsupervised Agent (IsolationForest)...")
    iso_forest_model = IsolationForest(random_state=42, contamination=anomaly_percentage, n_jobs=-1)

    # Now, use y_train_sup_reset to index X_train_scaled_df
    # We train Isolation Forest often only on 'normal' data (label 1)
    iso_forest_model.fit(X_train_scaled_df[y_train_sup_reset == 1])

    # Save models and scaler
    joblib.dump(rf_model, f"{models_dir}supervised_model.pkl")
    joblib.dump(iso_forest_model, f"{models_dir}unsupervised_model.pkl")
    joblib.dump(scaler, f"{models_dir}scaler.pkl") # Save the scaler as well

    logger.info("Models and scaler saved successfully.")
    logger.info("Training complete.")

    return rf_model, iso_forest_model, scaler, feature_names, X_train_sup # Return X_train_sup for XAI background data

if __name__ == "__main__":
    # Ensure the models directory exists
    if not os.path.exists("models/"):
        os.makedirs("models/")
    if not os.path.exists("data/processed/"):
        os.makedirs("data/processed/")

    train_and_save_models()