# check_features.py
import joblib
import os

try:
    cicids_feature_names = joblib.load("models/cicids_feature_names.pkl")
    print("Features loaded from models/cicids_feature_names.pkl:")
    print(cicids_feature_names)
    print(f"Number of features: {len(cicids_feature_names)}")
except FileNotFoundError:
    print("Error: models/cicids_feature_names.pkl not found. Please run train_models.py first.")