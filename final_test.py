#!/usr/bin/env python3

import requests
import json
import time

def test_anomaly_detection_api():
    """Test the anomaly detection API endpoints"""
    base_url = "http://localhost:5000"
    
    print("=== Testing ANMS API ===")
    
    # Test if the app is running
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✓ App is running successfully")
        else:
            print(f"✗ App returned status code: {response.status_code}")
            return
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to app: {e}")
        return
    
    # Test anomaly detection endpoint multiple times
    print("\n=== Testing Anomaly Detection ===")
    anomaly_count = 0
    normal_count = 0
    
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/detect_anomaly", timeout=10)
            if response.status_code == 200:
                data = response.json()
                log_entry = data.get('log_entry', {})
                
                is_anomaly = log_entry.get('is_anomaly', False)
                final_prediction = log_entry.get('final_prediction', -1)
                sup_pred = log_entry.get('supervised_prediction', -1)
                unsup_pred = log_entry.get('unsupervised_prediction', -1)
                sup_proba = log_entry.get('supervised_proba_anomaly', 0)
                unsup_score = log_entry.get('unsupervised_anomaly_score', 0)
                
                if is_anomaly:
                    anomaly_count += 1
                    status = "🚨 ANOMALY"
                else:
                    normal_count += 1
                    status = "✅ NORMAL"
                
                print(f"Test {i+1}: {status}")
                print(f"  Final: {final_prediction}, Supervised: {sup_pred} (prob: {sup_proba:.3f}), Unsupervised: {unsup_pred} (score: {unsup_score:.3f})")
                
                # Check if XAI explanation is provided for anomalies
                xai_explanation = log_entry.get('xai_explanation')
                if is_anomaly and xai_explanation:
                    print(f"  XAI: {len(xai_explanation.get('top_features', []))} features explained")
                elif is_anomaly:
                    print(f"  XAI: No explanation provided")
                
            else:
                print(f"✗ Detection test {i+1} failed with status: {response.status_code}")
            
            time.sleep(0.5)  # Small delay between requests
            
        except requests.exceptions.RequestException as e:
            print(f"✗ Detection test {i+1} failed: {e}")
    
    print(f"\n=== Results Summary ===")
    print(f"Total tests: 10")
    print(f"Anomalies detected: {anomaly_count}")
    print(f"Normal traffic: {normal_count}")
    print(f"Anomaly detection rate: {anomaly_count/10*100:.1f}%")
    
    # Test logs endpoint
    print(f"\n=== Testing Logs Endpoint ===")
    try:
        response = requests.get(f"{base_url}/get_latest_logs", timeout=5)
        if response.status_code == 200:
            logs = response.json()
            print(f"✓ Retrieved {len(logs)} log entries")
            
            if logs:
                latest_log = logs[0]
                print(f"Latest log timestamp: {latest_log.get('timestamp', 'N/A')}")
                print(f"Latest log anomaly status: {latest_log.get('is_anomaly', 'N/A')}")
        else:
            print(f"✗ Logs endpoint failed with status: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Logs endpoint failed: {e}")
    
    print(f"\n=== Test Complete ===")
    if anomaly_count > 0:
        print("🎉 SUCCESS: System is detecting anomalies!")
        print("The improved dummy data generation and model training is working.")
        print("Both supervised and unsupervised models are contributing to detection.")
    else:
        print("⚠️  WARNING: No anomalies detected in 10 tests.")
        print("This could be due to random chance or model configuration.")

if __name__ == "__main__":
    test_anomaly_detection_api()
