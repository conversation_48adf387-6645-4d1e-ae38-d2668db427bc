import pickle
import os

MODELS_DIR = 'models'
FEATURE_COLUMNS_PATH = os.path.join(MODELS_DIR, 'feature_columns.pkl')

try:
    with open(FEATURE_COLUMNS_PATH, 'rb') as f:
        feature_columns = pickle.load(f)
    print("Loaded feature columns:")
    print(feature_columns)
    print(f"\nTotal number of features: {len(feature_columns)}")
except FileNotFoundError:
    print(f"Error: {FEATURE_COLUMNS_PATH} not found. Please ensure train_models.py has been run.")
except Exception as e:
    print(f"An error occurred: {e}")