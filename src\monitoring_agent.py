import pandas as pd
import numpy as np

class MonitoringAgent:
    def __init__(self):
        print("MonitoringAgent: Initialized.")

    def collect_and_preprocess(self, raw_flow_data):
        """
        Simulates collection and initial preprocessing of a single network flow.
        For this simulation, it expects a dictionary of raw flow features.
        This function now ensures the output DataFrame has all 77 expected features.
        """
        # Define the exact 77 features from your feature_columns.pkl
        # This list MUST match the output you just got from get_features.py
        expected_features = [
            'Destination Port', 'Flow Duration', 'Total Fwd Packets', 'Total Backward Packets',
            'Total Length of Fwd Packets', 'Total Length of Bwd Packets', 'Fwd Packet Length Max',
            'Fwd Packet Length Min', 'Fwd Packet Length Mean', 'Fwd Packet Length Std',
            'Bwd Packet Length Max', 'Bwd Packet Length Min', 'Bwd Packet Length Mean',
            'Bwd Packet Length Std', 'Flow Bytes/s', 'Flow Packets/s', 'Flow IAT Mean',
            'Flow IAT Std', 'Flow IAT Max', 'Flow IAT Min', 'Fwd IAT Total', 'Fwd IAT Mean',
            'Fwd IAT Std', 'Fwd IAT Max', 'Fwd IAT Min', 'Bwd IAT Total', 'Bwd IAT Mean',
            'Bwd IAT Std', 'Bwd IAT Max', 'Bwd IAT Min', 'Fwd PSH Flags', 'Bwd PSH Flags',
            'Fwd URG Flags', 'Bwd URG Flags', 'Fwd Header Length', 'Bwd Header Length',
            'Fwd Packets/s', 'Bwd Packets/s', 'Min Packet Length', 'Max Packet Length',
            'Packet Length Mean', 'Packet Length Std', 'Packet Length Variance', 'FIN Flag Count',
            'SYN Flag Count', 'RST Flag Count', 'PSH Flag Count', 'ACK Flag Count',
            'URG Flag Count', 'CWE Flag Count', 'ECE Flag Count', 'Down/Up Ratio',
            'Average Packet Size', 'Avg Fwd Segment Size', 'Avg Bwd Segment Size',
            'Fwd Avg Bytes/Bulk', 'Fwd Avg Packets/Bulk', 'Fwd Avg Bulk Rate',
            'Bwd Avg Bytes/Bulk', 'Bwd Avg Packets/Bulk', 'Bwd Avg Bulk Rate',
            'Subflow Fwd Packets', 'Subflow Fwd Bytes', 'Subflow Bwd Packets',
            'Subflow Bwd Bytes', 'Init_Win_bytes_forward', 'Init_Win_bytes_backward',
            'act_data_pkt_fwd', 'min_seg_size_forward', 'Active Mean', 'Active Std',
            'Active Max', 'Active Min', 'Idle Mean', 'Idle Std', 'Idle Max', 'Idle Min'
        ]

        # Convert dictionary to DataFrame for consistency
        # Create an empty DataFrame with all expected columns
        flow_df = pd.DataFrame(columns=expected_features)
        
        # Add the raw_flow_data into a new row in this DataFrame
        # Use .iloc[0] if it's a single row after concat to return a Series if needed by agents
        # For a single flow, it's easier to create a Series and then convert to DataFrame
        temp_series = pd.Series(raw_flow_data)
        
        # Ensure all columns in temp_series are in flow_df, fill missing in temp_series with NaN
        for col in expected_features:
            if col not in temp_series.index:
                temp_series[col] = np.nan # Use NaN to distinguish from 0

        # Create DataFrame from the series, ensuring columns are in order
        flow_df = pd.DataFrame([temp_series[expected_features].values], columns=expected_features)

        # Basic cleaning/type conversion for numerical features
        for col in flow_df.columns:
            # Check if column is numeric or can be converted
            if pd.api.types.is_numeric_dtype(flow_df[col]) or pd.api.types.is_object_dtype(flow_df[col]):
                flow_df[col] = pd.to_numeric(flow_df[col], errors='coerce')
                # Replace Inf values with a large finite number (consistent with DataPreprocessor)
                if flow_df[col].isin([np.inf, -np.inf]).any():
                    flow_df[col] = flow_df[col].replace([np.inf, -np.inf], np.finfo(np.float64).max)
        
        # Fill any remaining NaNs with 0 (or a more suitable default for your data)
        # This is crucial for features that might not be generated by simulated_traffic
        flow_df = flow_df.fillna(0.0)

        # Ensure numeric type for all columns
        for col in flow_df.columns:
            if not pd.api.types.is_numeric_dtype(flow_df[col]):
                flow_df[col] = pd.to_numeric(flow_df[col], errors='coerce').fillna(0.0)


        # Select and reorder columns to match the trained model's feature order
        collected_data = flow_df[expected_features]
        
        print(f"MonitoringAgent: Collected and prepared {collected_data.shape[1]} features for analysis.")
        return collected_data