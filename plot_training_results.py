#!/usr/bin/env python3

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc
from sklearn.metrics import precision_recall_curve, average_precision_score
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set style for better looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def create_plots_directory():
    """Create directory for saving plots"""
    plots_dir = "plots"
    os.makedirs(plots_dir, exist_ok=True)
    return plots_dir

def plot_data_distribution(df, plots_dir):
    """Plot the distribution of normal vs anomaly data"""
    logger.info("Creating data distribution plots...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Data Distribution Analysis', fontsize=16, fontweight='bold')
    
    # 1. Label distribution
    label_counts = df['Label'].value_counts()
    axes[0, 0].pie(label_counts.values, labels=['Anomaly (0)', 'Normal (1)'], autopct='%1.1f%%', 
                   colors=['#ff6b6b', '#4ecdc4'])
    axes[0, 0].set_title('Label Distribution')
    
    # 2. Feature value ranges comparison
    normal_data = df[df['Label'] == 1].drop('Label', axis=1)
    anomaly_data = df[df['Label'] == 0].drop('Label', axis=1)
    
    feature_means_normal = normal_data.mean()
    feature_means_anomaly = anomaly_data.mean()
    
    # Select top 10 features with highest difference
    feature_diff = abs(feature_means_anomaly - feature_means_normal)
    top_features = feature_diff.nlargest(10).index
    
    x_pos = np.arange(len(top_features))
    axes[0, 1].bar(x_pos - 0.2, feature_means_normal[top_features], 0.4, 
                   label='Normal', color='#4ecdc4', alpha=0.7)
    axes[0, 1].bar(x_pos + 0.2, feature_means_anomaly[top_features], 0.4, 
                   label='Anomaly', color='#ff6b6b', alpha=0.7)
    axes[0, 1].set_xlabel('Features')
    axes[0, 1].set_ylabel('Mean Values')
    axes[0, 1].set_title('Top 10 Discriminative Features')
    axes[0, 1].set_xticks(x_pos)
    axes[0, 1].set_xticklabels(top_features, rotation=45, ha='right')
    axes[0, 1].legend()
    
    # 3. Distribution of first few features
    sample_features = df.columns[1:5]  # Skip 'Label', take first 4 features
    for i, feature in enumerate(sample_features):
        if i < 2:
            row, col = 1, i
            axes[row, col].hist(normal_data[feature], bins=30, alpha=0.7, 
                               label='Normal', color='#4ecdc4', density=True)
            axes[row, col].hist(anomaly_data[feature], bins=30, alpha=0.7, 
                               label='Anomaly', color='#ff6b6b', density=True)
            axes[row, col].set_xlabel(feature)
            axes[row, col].set_ylabel('Density')
            axes[row, col].set_title(f'Distribution: {feature}')
            axes[row, col].legend()
    
    plt.tight_layout()
    plt.savefig(f'{plots_dir}/data_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()
    logger.info(f"Data distribution plot saved to {plots_dir}/data_distribution.png")

def plot_model_performance(X_test, y_test, rf_model, iso_model, scaler, plots_dir):
    """Plot model performance metrics"""
    logger.info("Creating model performance plots...")
    
    # Get predictions
    X_test_scaled = scaler.transform(X_test)
    
    # Random Forest predictions
    rf_pred = rf_model.predict(X_test)
    rf_proba = rf_model.predict_proba(X_test)[:, 0]  # Probability of anomaly (class 0)
    
    # Isolation Forest predictions
    iso_pred_raw = iso_model.predict(X_test_scaled)
    iso_pred = np.where(iso_pred_raw == -1, 0, 1)
    iso_scores = iso_model.decision_function(X_test_scaled)
    iso_proba = 1 / (1 + np.exp(iso_scores))  # Convert to probability-like scores
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Model Performance Analysis', fontsize=16, fontweight='bold')
    
    # 1. Confusion Matrices
    cm_rf = confusion_matrix(y_test, rf_pred)
    cm_iso = confusion_matrix(y_test, iso_pred)
    
    sns.heatmap(cm_rf, annot=True, fmt='d', cmap='Blues', ax=axes[0, 0])
    axes[0, 0].set_title('Random Forest\nConfusion Matrix')
    axes[0, 0].set_xlabel('Predicted')
    axes[0, 0].set_ylabel('Actual')
    
    sns.heatmap(cm_iso, annot=True, fmt='d', cmap='Oranges', ax=axes[0, 1])
    axes[0, 1].set_title('Isolation Forest\nConfusion Matrix')
    axes[0, 1].set_xlabel('Predicted')
    axes[0, 1].set_ylabel('Actual')
    
    # 2. ROC Curves
    fpr_rf, tpr_rf, _ = roc_curve(y_test, 1 - rf_proba)  # 1 - prob for ROC
    roc_auc_rf = auc(fpr_rf, tpr_rf)
    
    fpr_iso, tpr_iso, _ = roc_curve(y_test, 1 - iso_proba)
    roc_auc_iso = auc(fpr_iso, tpr_iso)
    
    axes[0, 2].plot(fpr_rf, tpr_rf, color='blue', lw=2, 
                    label=f'Random Forest (AUC = {roc_auc_rf:.2f})')
    axes[0, 2].plot(fpr_iso, tpr_iso, color='orange', lw=2, 
                    label=f'Isolation Forest (AUC = {roc_auc_iso:.2f})')
    axes[0, 2].plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--')
    axes[0, 2].set_xlim([0.0, 1.0])
    axes[0, 2].set_ylim([0.0, 1.05])
    axes[0, 2].set_xlabel('False Positive Rate')
    axes[0, 2].set_ylabel('True Positive Rate')
    axes[0, 2].set_title('ROC Curves')
    axes[0, 2].legend(loc="lower right")
    
    # 3. Precision-Recall Curves
    precision_rf, recall_rf, _ = precision_recall_curve(1 - y_test, rf_proba)  # Invert for anomaly detection
    ap_rf = average_precision_score(1 - y_test, rf_proba)
    
    precision_iso, recall_iso, _ = precision_recall_curve(1 - y_test, iso_proba)
    ap_iso = average_precision_score(1 - y_test, iso_proba)
    
    axes[1, 0].plot(recall_rf, precision_rf, color='blue', lw=2, 
                    label=f'Random Forest (AP = {ap_rf:.2f})')
    axes[1, 0].plot(recall_iso, precision_iso, color='orange', lw=2, 
                    label=f'Isolation Forest (AP = {ap_iso:.2f})')
    axes[1, 0].set_xlabel('Recall')
    axes[1, 0].set_ylabel('Precision')
    axes[1, 0].set_title('Precision-Recall Curves')
    axes[1, 0].legend()
    
    # 4. Prediction Score Distributions
    axes[1, 1].hist(rf_proba[y_test == 1], bins=30, alpha=0.7, label='Normal', color='#4ecdc4')
    axes[1, 1].hist(rf_proba[y_test == 0], bins=30, alpha=0.7, label='Anomaly', color='#ff6b6b')
    axes[1, 1].set_xlabel('Random Forest Anomaly Probability')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('RF Score Distribution')
    axes[1, 1].legend()
    
    axes[1, 2].hist(iso_scores[y_test == 1], bins=30, alpha=0.7, label='Normal', color='#4ecdc4')
    axes[1, 2].hist(iso_scores[y_test == 0], bins=30, alpha=0.7, label='Anomaly', color='#ff6b6b')
    axes[1, 2].set_xlabel('Isolation Forest Anomaly Score')
    axes[1, 2].set_ylabel('Frequency')
    axes[1, 2].set_title('ISO Score Distribution')
    axes[1, 2].legend()
    
    plt.tight_layout()
    plt.savefig(f'{plots_dir}/model_performance.png', dpi=300, bbox_inches='tight')
    plt.show()
    logger.info(f"Model performance plot saved to {plots_dir}/model_performance.png")

def plot_feature_importance(rf_model, feature_names, plots_dir):
    """Plot feature importance from Random Forest"""
    logger.info("Creating feature importance plot...")
    
    # Get feature importance
    importance = rf_model.feature_importances_
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance
    }).sort_values('importance', ascending=False)
    
    # Plot top 20 features
    top_features = feature_importance_df.head(20)
    
    plt.figure(figsize=(12, 8))
    sns.barplot(data=top_features, y='feature', x='importance', palette='viridis')
    plt.title('Top 20 Feature Importance (Random Forest)', fontsize=14, fontweight='bold')
    plt.xlabel('Importance Score')
    plt.ylabel('Features')
    plt.tight_layout()
    plt.savefig(f'{plots_dir}/feature_importance.png', dpi=300, bbox_inches='tight')
    plt.show()
    logger.info(f"Feature importance plot saved to {plots_dir}/feature_importance.png")
    
    return feature_importance_df

def main():
    """Main function to generate all training plots"""
    logger.info("=== Generating Training Result Plots ===")
    
    # Create plots directory
    plots_dir = create_plots_directory()
    
    try:
        # Load data
        logger.info("Loading data...")
        df = pd.read_csv("data/processed/dummy_cicids_data.csv")
        X = df.drop('Label', axis=1)
        y = df['Label']
        feature_names = X.columns.tolist()
        
        # Load models
        logger.info("Loading trained models...")
        rf_model = joblib.load("models/supervised_model.pkl")
        iso_model = joblib.load("models/unsupervised_model.pkl")
        scaler = joblib.load("models/scaler.pkl")
        
        # Split data (same as training)
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Generate plots
        plot_data_distribution(df, plots_dir)
        plot_model_performance(X_test, y_test, rf_model, iso_model, scaler, plots_dir)
        feature_importance_df = plot_feature_importance(rf_model, feature_names, plots_dir)
        
        # Save feature importance to CSV
        feature_importance_df.to_csv(f'{plots_dir}/feature_importance.csv', index=False)
        logger.info(f"Feature importance saved to {plots_dir}/feature_importance.csv")
        
        logger.info("=== All plots generated successfully! ===")
        logger.info(f"Check the '{plots_dir}' directory for all visualizations.")
        
    except Exception as e:
        logger.error(f"Error generating plots: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
