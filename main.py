import pandas as pd
import os
import sys
from datetime import datetime
import numpy as np
import random
import joblib
import sqlite3
import time
import ipaddress
import threading # Import threading module for concurrent execution

# Add the 'src' directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import agent classes
from src.monitoring_agent import MonitoringAgent
from src.supervised_agent import SupervisedDetectionAgent
from src.unsupervised_anomaly_agent import UnsupervisedAnomalyAgent
from src.decision_agent import DecisionCoordinationAgent
from src.log_analyzer_agent import LogAnalyzerAgent # NEW: Import LogAnalyzerAgent

# Assuming models are in ANMS_PROJECT/models/
RF_MODEL_PATH = "models/rf_model.pkl"
ISO_MODEL_PATH = "models/iso_model.pkl"
SCALER_PATH = "models/scaler.pkl"
FEATURE_COLUMNS_PATH = "models/feature_columns.pkl"
DB_PATH = "logs/anms_log.db" # Define path for SQLite database

# --- CORRECTED: Node to IP Mapping (Now includes 'type' and other info) ---
# This dictionary now correctly includes all node attributes (pos, label, type, zone)
# and the assigned 'ip_range' for traffic generation.
nodes_info = {
    # --- INTERNET / WAN EDGE ---
    'internet_cloud':       {'pos': (-2, 10), 'label': 'Internet Cloud', 'type': 'external', 'zone': 'Internet', 'ip_range': '***********/24'},
    'isp_router':           {'pos': (0, 10), 'label': 'ISP Router', 'type': 'router', 'zone': 'Internet', 'ip_range': '***********/24'},
    'campus_border_router': {'pos': (2, 10), 'label': 'Campus Border Router', 'type': 'router', 'zone': 'Perimeter', 'ip_range': '**********/24'},
    'external_dns':         {'pos': (-0.5, 11), 'label': 'External DNS', 'type': 'server', 'zone': 'Internet', 'ip_range': '*******/32'},

    # --- CAMPUS CORE ---
    'core_router_1':        {'pos': (2, 8), 'label': 'Core Router 1', 'type': 'router', 'zone': 'Core', 'ip_range': '**********/24'},
    'core_router_2':        {'pos': (4, 8), 'label': 'Core Router 2', 'type': 'router', 'zone': 'Core', 'ip_range': '**********/24'},
    'main_firewall':        {'pos': (3, 9), 'label': 'Main Firewall', 'type': 'security', 'zone': 'Core', 'ip_range': '**********0/24'},
    'dns_server_campus':    {'pos': (1.5, 7.5), 'label': 'Campus DNS', 'type': 'server', 'zone': 'Core', 'ip_range': '**********0/24'},
    'dhcp_server_campus':   {'pos': (2.5, 7.5), 'label': 'Campus DHCP', 'type': 'server', 'zone': 'Core', 'ip_range': '**********1/24'},

    # --- DMZ (Demilitarized Zone) ---
    'dmz_firewall':         {'pos': (7, 8), 'label': 'DMZ Firewall', 'type': 'security', 'zone': 'DMZ', 'ip_range': '********/24'},
    'web_server_prod':      {'pos': (7, 7), 'label': 'Prod Web Svr', 'type': 'server', 'zone': 'DMZ', 'ip_range': '********0/24'},
    'student_portal_server':{'pos': (8, 7), 'label': 'Student Portal', 'type': 'server', 'zone': 'DMZ', 'ip_range': '********1/24'},
    'email_server':         {'pos': (6, 7), 'label': 'Email Server', 'type': 'server', 'zone': 'DMZ', 'ip_range': '********2/24'},

    # --- ACADEMIC BUILDING A (e.g., Computer Science Dept) ---
    'building_a_l3_switch': {'pos': (0, 6), 'label': 'Bldg A L3 Switch', 'type': 'switch', 'zone': 'Bldg_A', 'ip_range': None},
    'cs_lab_switch':        {'pos': (-1, 5), 'label': 'CS Lab Switch', 'type': 'switch', 'zone': 'Bldg_A', 'ip_range': None},
    'cs_lab_pc_1':          {'pos': (-1.5, 4), 'label': 'CS Lab PC 1', 'type': 'workstation', 'zone': 'Bldg_A', 'ip_range': '************/24'},
    'cs_lab_pc_2':          {'pos': (-0.5, 4), 'label': 'CS Lab PC 2', 'type': 'workstation', 'zone': 'Bldg_A', 'ip_range': '************/24'},
    'cs_prof_office_switch':{'pos': (1, 5), 'label': 'Prof Office Switch', 'type': 'switch', 'zone': 'Bldg_A', 'ip_range': None},
    'cs_prof_pc_1':         {'pos': (0.5, 4), 'label': 'Prof PC 1', 'type': 'workstation', 'zone': 'Bldg_A', 'ip_range': '************/24'},
    'cs_prof_laptop_1':     {'pos': (1.5, 4), 'label': 'Prof Laptop 1', 'type': 'workstation', 'zone': 'Bldg_A', 'ip_range': '************/24'},
    'cs_printer':           {'pos': (0, 3), 'label': 'CS Printer', 'type': 'iot', 'zone': 'Bldg_A', 'ip_range': '*************/24'},
    'cs_ap_1':              {'pos': (0.5, 6.5), 'label': 'CS AP 1', 'type': 'wireless_ap', 'zone': 'Bldg_A', 'ip_range': None},
    'student_phone_1':      {'pos': (-0.5, 6), 'label': 'Student Phone', 'type': 'mobile', 'zone': 'Bldg_A', 'ip_range': '************/24'},
    'prof_tablet_1':        {'pos': (1.5, 6), 'label': 'Prof Tablet', 'type': 'mobile', 'zone': 'Bldg_A', 'ip_range': '************/24'},

    # --- ADMINISTRATION BUILDING B ---
    'building_b_l3_switch': {'pos': (4, 6), 'label': 'Bldg B L3 Switch', 'type': 'switch', 'zone': 'Bldg_B', 'ip_range': None},
    'admin_office_switch':  {'pos': (3, 5), 'label': 'Admin Office Switch', 'type': 'switch', 'zone': 'Bldg_B', 'ip_range': None},
    'hr_workstation':       {'pos': (2.5, 4), 'label': 'HR Workstation', 'type': 'workstation', 'zone': 'Bldg_B', 'ip_range': '************/24'},
    'finance_workstation':  {'pos': (3.5, 4), 'label': 'Finance Workstation', 'type': 'workstation', 'zone': 'Bldg_B', 'ip_range': '************/24'},
    'building_b_ap_1':      {'pos': (4.5, 6.5), 'label': 'Bldg B AP 1', 'type': 'wireless_ap', 'zone': 'Bldg_B', 'ip_range': None},
    'guest_laptop_1':       {'pos': (3.5, 6), 'label': 'Guest Laptop', 'type': 'mobile', 'zone': 'Bldg_B', 'ip_range': '************/24'},
    'ip_phone_admin':       {'pos': (5, 5), 'label': 'Admin IP Phone', 'type': 'iot', 'zone': 'Bldg_B', 'ip_range': '************0/24'},

    # --- RESEARCH LAB (Isolated Network) ---
    'research_router':      {'pos': (9, 4.5), 'label': 'Research Router', 'type': 'router', 'zone': 'Research_Lab', 'ip_range': '***********/24'},
    'research_firewall':    {'pos': (9, 3.5), 'label': 'Research Firewall', 'type': 'security', 'zone': 'Research_Lab', 'ip_range': '***********/24'},
    'hpc_cluster_head':     {'pos': (8.5, 2.5), 'label': 'HPC Cluster Head', 'type': 'server', 'zone': 'Research_Lab', 'ip_range': '***********0/24'},
    'data_storage_server':  {'pos': (9.5, 2.5), 'label': 'Data Storage Svr', 'type': 'server', 'zone': 'Research_Lab', 'ip_range': '***********1/24'},
    'lab_instrument_1':     {'pos': (8.5, 1.5), 'label': 'Instrument 1', 'type': 'iot', 'zone': 'Research_Lab', 'ip_range': '***********00/24'},
    'research_pc_1':        {'pos': (9.5, 1.5), 'label': 'Research PC 1', 'type': 'workstation', 'zone': 'Research_Lab', 'ip_range': '***********2/24'},
    'research_ap_1':        {'pos': (10, 3.5), 'label': 'Research AP 1', 'type': 'wireless_ap', 'zone': 'Research_Lab', 'ip_range': None},
    'researcher_tablet':    {'pos': (10.5, 2.5), 'label': 'Researcher Tablet', 'type': 'mobile', 'zone': 'Research_Lab', 'ip_range': '************/24'},

    # --- ANMS Agents ---
    'monitoring_agent_core':    {'pos': (1.5, 8.5), 'label': 'Monitoring Agent (Core)', 'type': 'agent', 'zone': 'Core', 'ip_range': None},
    'sda_agent':                {'pos': (3, 8.5), 'label': 'SDA Agent', 'type': 'agent', 'zone': 'Core', 'ip_range': None},
    'uaa_agent':                {'pos': (3, 7.5), 'label': 'UAA Agent', 'type': 'agent', 'zone': 'Core', 'ip_range': None},
    'decision_agent':           {'pos': (3, 6.5), 'label': 'Decision Agent', 'type': 'agent', 'zone': 'Core', 'ip_range': None},
    'log_analyzer_agent':       {'pos': (5.5, 7.5), 'label': 'Log Analyzer Agent', 'type': 'agent', 'zone': 'Core', 'ip_range': None}
}


def get_random_ip_from_range(ip_range_str):
    """Generates a random IP address within a given CIDR range."""
    if ip_range_str is None:
        return '0.0.0.0' # Return a placeholder if no IP range is defined

    try:
        network = ipaddress.ip_network(ip_range_str, strict=False)
        # Exclude network and broadcast addresses for usable IPs
        if network.prefixlen == 32: # Single host IP
            return str(network.network_address)
        
        hosts = list(network.hosts())
        if not hosts:
            return str(network.network_address) # Fallback for tiny subnets
        return str(random.choice(hosts))
    except ValueError:
        return '0.0.0.0' # Invalid IP range string

def map_ip_to_node_id(ip_address_str):
    """Maps an IP address back to a conceptual node ID in the dashboard topology."""
    try:
        ip_obj = ipaddress.ip_address(ip_address_str)
        for node_id, info in nodes_info.items():
            if 'ip_range' in info and info['ip_range']: # Check if ip_range exists and is not None
                try:
                    network = ipaddress.ip_network(info['ip_range'], strict=False)
                    if ip_obj in network:
                        return node_id
                except ValueError:
                    # Handle cases where ip_range might be a single IP not a range (e.g. for routers)
                    if str(ip_obj) == info['ip_range'].split('/')[0]:
                        return node_id
        return 'unknown' # If no mapping found
    except ValueError:
        return 'invalid_ip'


def initialize_db():
    """Initializes the SQLite database and creates the 'decisions' table."""
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS decisions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            flow_id INTEGER,
            source_ip TEXT,
            dest_ip TEXT,
            source_port INTEGER,
            dest_port INTEGER,
            sda_result TEXT,
            sda_confidence REAL,
            uaa_result TEXT,
            uaa_score REAL,
            final_decision TEXT,
            action TEXT,
            log_message TEXT
        )
    ''')
    conn.commit()
    conn.close()
    print(f"SQLite database initialized at {DB_PATH}")


def generate_simulated_traffic_data(num_samples=10, feature_columns_list=None):
    """
    Generates dummy network traffic data for demonstration, matching the features
    used by the trained models, with IPs from the defined topology.
    """
    if feature_columns_list is None:
        raise ValueError("feature_columns_list must be provided to generate simulated data.")

    simulated_data = []
    np.random.seed(42)

    traffic_generating_nodes = [
        node_id for node_id, info in nodes_info.items()
        if info['type'] not in ['agent', 'router', 'switch', 'security', 'external', 'wireless_ap'] and info['ip_range'] is not None
    ]
    traffic_ip_ranges = [nodes_info[node_id]['ip_range'] for node_id in traffic_generating_nodes]
    
    target_nodes = [
        node_id for node_id, info in nodes_info.items()
        if info['type'] not in ['agent', 'router', 'switch', 'wireless_ap', 'external'] and info['ip_range'] is not None
    ]
    target_ip_ranges = [nodes_info[node_id]['ip_range'] for node_id in target_nodes]
    
    target_ip_ranges.extend([
        nodes_info['campus_border_router']['ip_range'],
        nodes_info['main_firewall']['ip_range'],
        nodes_info['core_router_1']['ip_range'],
        nodes_info['core_router_2']['ip_range'],
        nodes_info['dmz_firewall']['ip_range'],
    ])
    target_ip_ranges = list(set([ip for ip in target_ip_ranges if ip is not None]))


    for i in range(num_samples):
        flow_data = {}
        for feature in feature_columns_list:
            if 'port' in feature:
                flow_data[feature] = np.random.randint(1, 65535)
            elif 'flag' in feature or 'cnt' in feature:
                flow_data[feature] = np.random.randint(0, 5)
            elif 'total_fwd_packets' in feature or 'total_bwd_packets' in feature:
                flow_data[feature] = np.random.randint(1, 200)
            elif 'total_len' in feature:
                flow_data[feature] = np.random.randint(100, 50000)
            elif 'mean' in feature or 'std' in feature or 'variance' in feature or 'avg' in feature:
                flow_data[feature] = np.round(np.random.uniform(10, 1500), 2)
            elif 'duration' in feature or 'iat' in feature or 'idle' in feature or 'active' in feature:
                flow_data[feature] = np.round(np.random.exponential(scale=1000), 2)
            elif 'min_pkt_len' in feature:
                flow_data[feature] = np.random.randint(40, 100)
            elif 'max_pkt_len' in feature:
                flow_data[feature] = np.random.randint(100, 1500)
            elif 'protocol' in feature:
                 flow_data[feature] = np.random.choice([6, 17])
            else:
                flow_data[feature] = np.round(np.random.rand() * 100, 2)

        src_range = random.choice(traffic_ip_ranges)
        dst_range = random.choice(target_ip_ranges)
        
        flow_data['Source IP'] = get_random_ip_from_range(src_range)
        flow_data['Destination IP'] = get_random_ip_from_range(dst_range)
        flow_data['Source Port'] = random.randint(1024, 65535)
        flow_data['Destination Port'] = random.randint(1, 65535)
        flow_data['Flow ID'] = i + 1

        if np.random.rand() < 0.35: # Increased to 35% chance of being an "anomaly" for demo
            flow_data['Source IP'] = get_random_ip_from_range(nodes_info['cs_lab_pc_1']['ip_range'])
            flow_data['Destination IP'] = get_random_ip_from_range(nodes_info['web_server_prod']['ip_range'])
            
            flow_data['total_fwd_packets'] = np.random.randint(1000, 5000)
            flow_data['total_len_fwd_packets'] = np.random.randint(50000, 200000)
            flow_data['duration'] = np.round(np.random.uniform(100, 500), 2)
            flow_data['Idle Mean'] = np.round(np.random.uniform(0, 1), 2)
            flow_data['Active Mean'] = np.round(np.random.uniform(100, 500), 2)
            
            if 'min_seg_size_forward' in feature_columns_list:
                flow_data['min_seg_size_forward'] = 20
            if 'protocol' in feature_columns_list:
                flow_data['protocol'] = 6
            if 'dest_port' in feature_columns_list:
                flow_data['dest_port'] = random.choice([80, 443, 22, 23, 53, 3389])

        simulated_data.append(flow_data)

    df = pd.DataFrame(simulated_data)
    
    for col in feature_columns_list:
        if col not in df.columns:
            df[col] = 0.0
    
    for col in feature_columns_list:
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(np.float32)
    
    df = df[feature_columns_list]

    return df


def run_multi_agent_system(traffic_data, db_path):
    """
    Orchestrates the multi-agent system to process network traffic.
    """
    print("Initializing Multi-Agent System...")
    ma = MonitoringAgent()
    sda = SupervisedDetectionAgent(model_path=RF_MODEL_PATH, scaler_path=SCALER_PATH)
    uaa = UnsupervisedAnomalyAgent(model_path=ISO_MODEL_PATH, scaler_path=SCALER_PATH)
    dca = DecisionCoordinationAgent(db_path=db_path) 
    
    # NEW: Initialize and start the Log Analyzer Agent in a separate thread
    log_analyzer = LogAnalyzerAgent(db_path=DB_PATH)
    # Target function for the thread is run_analysis_loop
    # daemon=True means the thread will exit when the main program exits
    analyzer_thread = threading.Thread(target=log_analyzer.run_analysis_loop, args=(10, 5), daemon=True)
    analyzer_thread.start()
    print("Log Analyzer Agent thread started.")

    print("\nStarting traffic processing simulation...\n")
    
    for i, row_series in traffic_data.iterrows():
        print(f"\n--- Processing Traffic Flow {i+1} ---")
        
        raw_data_dict = traffic_data.iloc[i].to_dict()
        observed_data = {
            'Flow ID': i + 1,
            'Source IP': raw_data_dict.get('Source IP', 'N/A'),
            'Destination IP': raw_data_dict.get('Destination IP', 'N/A'),
            'Source Port': raw_data_dict.get('Source Port', -1),
            'Destination Port': raw_data_dict.get('Destination Port', -1),
        }

        print(f"  Source IP: {observed_data.get('Source IP', 'N/A')}")
        print(f"  Destination IP: {observed_data.get('Destination IP', 'N/A')}")
        print(f"  Source Port: {observed_data.get('Source Port', 'N/A')}")
        print(f"  Destination Port: {observed_data.get('Destination Port', 'N/A')}")
        
        sda_preprocessed_df = sda.preprocess_for_detection(row_series)
        sda_result, sda_confidence = sda.detect(sda_preprocessed_df)
        print(f"  SDA Result: {sda_result} (Confidence: {sda_confidence:.2f})")
        
        uaa_preprocessed_df = uaa.preprocess_for_anomaly(row_series)
        uaa_result, uaa_score = uaa.detect_anomaly(uaa_preprocessed_df)
        print(f"  UAA Result: {uaa_result} (Anomaly Score: {uaa_score:.2f})")
        
        final_decision, action = dca.make_decision(
            sda_result, sda_confidence, uaa_result, uaa_score, observed_data
        )
        print(f"  Final Decision: {final_decision} | Action: {action}")
        
        time.sleep(1.0)
        
    print("\n--- Simulation Complete ---")
    print(f"All decisions logged to: {DB_PATH}")


if __name__ == "__main__":
    if not os.path.exists(RF_MODEL_PATH) or \
       not os.path.exists(ISO_MODEL_PATH) or \
       not os.path.exists(SCALER_PATH) or \
       not os.path.exists(FEATURE_COLUMNS_PATH):
        print(f"Error: Required model files not found.")
        print(f"Please ensure '{RF_MODEL_PATH}', '{ISO_MODEL_PATH}', '{SCALER_PATH}', and '{FEATURE_COLUMNS_PATH}' exist in the 'models/' directory.")
        print("You may need to re-run 'train_models.py' first to generate them.")
        sys.exit(1)

    initialize_db()

    try:
        trained_feature_columns = joblib.load(FEATURE_COLUMNS_PATH)
        print(f"Loaded {len(trained_feature_columns)} feature columns from {FEATURE_COLUMNS_PATH}.")
    except Exception as e:
        print(f"Error loading feature columns from {FEATURE_COLUMNS_PATH}: {e}")
        sys.exit(1)

    simulated_traffic = generate_simulated_traffic_data(num_samples=40, feature_columns_list=trained_feature_columns)

    run_multi_agent_system(simulated_traffic, DB_PATH)                                                                             