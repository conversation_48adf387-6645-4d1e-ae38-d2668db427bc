#!/usr/bin/env python3

import json
import pandas as pd
from decision_agent import DecisionAgent

def test_json_serialization():
    """Test that the decision agent output is JSON serializable"""
    print("=== Testing JSON Serialization Fix ===")
    
    try:
        # Load data and initialize agent
        df = pd.read_csv("data/processed/dummy_cicids_data.csv")
        X = df.drop('Label', axis=1)
        y = df['Label']
        feature_names = X.columns.tolist()
        
        decision_agent = DecisionAgent(
            feature_names=feature_names,
            supervised_model_path="models/supervised_model.pkl",
            unsupervised_model_path="models/unsupervised_model.pkl",
            scaler_path="models/scaler.pkl"
        )
        print("✓ Decision agent initialized")
        
        # Test with an anomaly sample
        anomaly_sample = X[y == 0].iloc[0:1]
        result = decision_agent.make_decision(anomaly_sample)
        
        print(f"✓ Decision made: {result['final_prediction']} (anomaly: {result['is_anomaly']})")
        
        # Try to serialize to JSON
        json_str = json.dumps(result, indent=2)
        print("✓ JSON serialization successful!")
        
        # Parse it back to verify
        parsed = json.loads(json_str)
        print("✓ JSON parsing successful!")
        
        # Show some key results
        print(f"\nResults:")
        print(f"  Final prediction: {parsed['final_prediction']}")
        print(f"  Is anomaly: {parsed['is_anomaly']}")
        print(f"  Supervised prediction: {parsed['supervised_prediction']}")
        print(f"  Unsupervised prediction: {parsed['unsupervised_prediction']}")
        
        if parsed['xai_explanation']:
            print(f"  XAI explanation available with {len(parsed['xai_explanation']['top_features'])} features")
            print(f"  Top feature: {parsed['xai_explanation']['top_features'][0]['name']}")
        
        print("\n🎉 SUCCESS: JSON serialization is now working!")
        print("The Flask app should now work without errors.")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_json_serialization()
