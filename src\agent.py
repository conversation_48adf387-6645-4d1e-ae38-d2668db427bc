# Modified agent.py
import pandas as pd
import joblib
from datetime import datetime, timedelta
import random

class Agent:
    def __init__(self, name, agent_id, rf_model_path, iso_model_path, flags_encoder_path):
        self.agent_id = agent_id
        self.name = name
        self.rf_model = joblib.load(rf_model_path)
        self.iso_model = joblib.load(iso_model_path)
        self.flags_encoder = None # No longer needed if 'flags' is numerical

        # Expanded feature columns list, must match data_preprocessor and train_models
        self.feature_columns = [
            'dest_port', 'duration', 'total_fwd_packets', 'total_bwd_packets',
            'total_len_fwd_packets', 'total_len_bwd_packets', 'fwd_pkt_len_mean',
            'bwd_pkt_len_mean', 'flow_iat_mean', 'flow_iat_max', 'flow_iat_min',
            'syn_flag_count', 'ack_flag_count', 'rst_flag_count', 'urg_flag_count',
            'cwe_flag_count', 'ece_flag_count', 'flags', # PSH Flag Count
            'pkt_len_mean', 'pkt_len_std', 'pkt_len_variance', 'min_pkt_len', 'max_pkt_len'
        ]
        self.logs = []
        self.start_time = datetime.now()

    def preprocess(self, row):
        if isinstance(row, pd.Series) or isinstance(row, dict):
            df = pd.DataFrame([row])
        elif isinstance(row, pd.DataFrame):
            df = row.copy()
        else:
            raise TypeError("Unsupported data type passed to preprocess")

        df.reset_index(drop=True, inplace=True)

        # Ensure all feature columns are present before returning
        try:
            df = df[self.feature_columns]
        except KeyError as e:
            missing_cols = [col for col in self.feature_columns if col not in df.columns]
            raise KeyError(f"Some required features are missing in the input data for preprocessing: {missing_cols}. Original error: {e}")

        # Ensure numerical types
        for col in self.feature_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        df.dropna(inplace=True) # Drop rows if any feature becomes NaN after conversion

        return df

    def monitor(self, traffic_row):
        print(f"[{self.agent_id}] Monitoring traffic...")
        return traffic_row

    def detect(self, row_df):
        rf_pred = self.rf_model.predict(row_df)[0]
        iso_pred = self.iso_model.predict(row_df)[0]
        is_threat = (rf_pred == 1) or (iso_pred == -1)
        return is_threat, rf_pred, iso_pred

    def respond(self, is_threat, original_data, index):
        action = "BLOCKED" if is_threat else "ALLOWED"
        timestamp = self.start_time + timedelta(seconds=index)

        # Populate log_entry with relevant features from original_data
        log_entry = {
            "Timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "Agent": self.agent_id,
            "Source IP": original_data.get('Source IP', 'N/A'), # Assuming these might exist in original data or default
            "Source Port": original_data.get('Source Port', 'N/A'),
            "Destination IP": original_data.get('Destination IP', 'N/A'),
            "Destination Port": original_data.get('dest_port', 'N/A'), # Use 'dest_port' from original_data

            # Include all expanded features from original_data
            "Duration": original_data.get('duration', 'N/A'),
            "Total Fwd Packets": original_data.get('total_fwd_packets', 'N/A'),
            "Total Bwd Packets": original_data.get('total_bwd_packets', 'N/A'),
            "Total Length Fwd Packets": original_data.get('total_len_fwd_packets', 'N/A'),
            "Total Length Bwd Packets": original_data.get('total_len_bwd_packets', 'N/A'),
            "Fwd Packet Length Mean": original_data.get('fwd_pkt_len_mean', 'N/A'),
            "Bwd Packet Length Mean": original_data.get('bwd_pkt_len_mean', 'N/A'),
            "Flow IAT Mean": original_data.get('flow_iat_mean', 'N/A'),
            "Flow IAT Max": original_data.get('flow_iat_max', 'N/A'),
            "Flow IAT Min": original_data.get('flow_iat_min', 'N/A'),
            "SYN Flag Count": original_data.get('syn_flag_count', 'N/A'),
            "ACK Flag Count": original_data.get('ack_flag_count', 'N/A'),
            "RST Flag Count": original_data.get('rst_flag_count', 'N/A'),
            "URG Flag Count": original_data.get('urg_flag_count', 'N/A'),
            "CWE Flag Count": original_data.get('cwe_flag_count', 'N/A'),
            "ECE Flag Count": original_data.get('ece_flag_count', 'N/A'),
            "PSH Flag Count (Flags)": original_data.get('flags', 'N/A'), # Renamed for clarity in log
            "Packet Length Mean": original_data.get('pkt_len_mean', 'N/A'),
            "Packet Length Std": original_data.get('pkt_len_std', 'N/A'),
            "Packet Length Variance": original_data.get('pkt_len_variance', 'N/A'),
            "Min Packet Length": original_data.get('min_pkt_len', 'N/A'),
            "Max Packet Length": original_data.get('max_pkt_len', 'N/A'),
            "Packet Size": original_data.get('packet_size', 'N/A'), 
            # Keep this for consistency with previous logs if it's 'Total Fwd Packets'
            # but it should now be from the actual data for 'packet_size'
            # which was Total Fwd Packets earlier, now it's Total Fwd Packets.
            # Let's clarify: if 'packet_size' in your feature_columns refers to
            # Total Fwd Packets, it's already included. If it's something else,
            # ensure it's mapped in data_preprocessor.
            "Label": original_data.get('Label', 'N/A'),
            "RF Result": int(is_threat),
            "iForest Result": original_data.get('iso_pred', 'N/A'), # Assuming iso_pred might be passed or derived
            "Action": action
        }

        # The 'total fwd packets' and 'psh flag count' were randomly generated before
        # and are now taken from original_data, reflecting the new features.
        # So these lines are removed:
        # total_fwd_packets = random.randint(1, 100)
        # psh_flag_count = random.randint(0, 10)

        print(f"[{self.agent_id}] {'⚠️' if is_threat else '✅'} Action: {action}")
        self.logs.append(log_entry)
        return action

    def run(self, traffic_row, index=0):
        print(f"\n--- Packet handled by {self.name} ---")
        print(f"[{self.name}] Monitoring traffic...")

        try:
            observed = self.monitor(traffic_row)
            row_df = self.preprocess(observed)
            is_threat, rf_pred, iso_pred = self.detect(row_df)

            # Pass iso_pred as part of original_data for logging in respond
            observed_with_results = observed.to_dict() if isinstance(observed, pd.Series) else observed.copy()
            observed_with_results['iso_pred'] = iso_pred # Add iForest result to original_data for logging

            action = self.respond(is_threat, observed_with_results, index) # Pass updated observed
            print(f"[{self.name}] Detection complete | RF: {rf_pred} | iForest: {iso_pred} | Action: {action}")
            return action

        except Exception as e:
            print(f"[{self.name}] Error during run: {e}")
            return "ERROR"

    def get_logs(self):
        return pd.DataFrame(self.logs)