# decision_agent.py

import numpy as np
import pandas as pd
import logging

from supervised_agent import SupervisedAgent
from unsupervised_agent import UnsupervisedAgent
from src.utils.xai_explainer import XAI_Explainer  # Corrected import path for XAI Explainer

logger = logging.getLogger(__name__)


class DecisionAgent:
    def __init__(self, feature_names,
                 supervised_model_path="models/supervised_model.pkl",
                 unsupervised_model_path="models/unsupervised_model.pkl",
                 scaler_path="models/scaler.pkl"):
        """
        Initializes the Decision Agent with Supervised and Unsupervised Agents,
        and the XAI Explainer.
        """
        self.supervised_agent = SupervisedAgent(supervised_model_path)
        self.unsupervised_agent = UnsupervisedAgent(unsupervised_model_path, scaler_path)
        self.xai_explainer = None  # Will be initialized later once background data is available
        self.feature_names = feature_names
        logger.info("DecisionAgent initialized.")

    def initialize_xai_explainer(self, model, background_data_for_shap, target_class_index=0):
        """
        Initializes the XAI Explainer. Call after model training and access to representative background data.
        """
        self.xai_explainer = XAI_Explainer(model, self.feature_names)
        self.xai_explainer.build_explainer(background_data_for_shap, target_class_index)
        logger.info(f"XAI Explainer initialized for target class index {target_class_index}.")

    def make_decision(self, instance_data):
        """
        Makes a decision using predictions from both agents and provides an XAI explanation if anomaly is detected.

        Args:
            instance_data (pd.DataFrame or np.ndarray): A single row of data.

        Returns:
            dict: Decision log including predictions, scores, and explanation (if applicable).
        """
        # Ensure instance_data is a DataFrame
        if not isinstance(instance_data, pd.DataFrame):
            if isinstance(instance_data, np.ndarray):
                if instance_data.ndim == 1:
                    instance_data = pd.DataFrame([instance_data], columns=self.feature_names)
                elif instance_data.ndim == 2:
                    instance_data = pd.DataFrame(instance_data, columns=self.feature_names)
                else:
                    logger.error("Unsupported array shape for instance_data.")
                    return {"prediction": -1, "reason": "Invalid input data shape", "xai_explanation": None}
            else:
                logger.error("instance_data must be a pandas DataFrame or numpy array.")
                return {"prediction": -1, "reason": "Invalid input data type", "xai_explanation": None}

        # Get prediction from Supervised Agent
        sup_pred = self.supervised_agent.predict(instance_data)[0]
        sup_proba = self.supervised_agent.predict_proba(instance_data)[0]  # [prob_class_0, prob_class_1]

        # Get prediction from Unsupervised Agent
        un_pred_raw = self.unsupervised_agent.predict(instance_data)[0]
        un_pred = 0 if un_pred_raw == -1 else 1  # Convert -1 (anomaly) to 0

        # Get anomaly score
        un_score = self.unsupervised_agent.decision_function(instance_data)[0]

        # Voting Mechanism
        votes = [sup_pred, un_pred]
        anomaly_votes = votes.count(0)
        normal_votes = votes.count(1)
        final_prediction = 0 if anomaly_votes > normal_votes else 1  # Default to normal if tie

        # Generate XAI explanation if anomaly
        xai_explanation_summary = None
        if final_prediction == 0 and self.xai_explainer:
            logger.info("Anomaly detected. Generating XAI explanation.")
            explanation = self.xai_explainer.explain_instance(instance_data.iloc[0])
            if explanation:
                top_features = self.xai_explainer.get_top_features(explanation, num_features=5)
                xai_explanation_summary = {
                    "base_value": float(explanation.base_values[0]) if hasattr(explanation, 'base_values') else None,
                    "top_features": [{"name": name, "value": float(val)} for name, val in top_features]
                }
                logger.info(f"XAI explanation generated: {xai_explanation_summary}")
            else:
                logger.error("Failed to generate XAI explanation.")

        # Decision log
        decision_log = {
            "timestamp": pd.Timestamp.now().isoformat(),
            "instance_id": hash(frozenset(instance_data.values[0])),
            "supervised_prediction": int(sup_pred),
            "supervised_proba_anomaly": float(sup_proba[0]),
            "unsupervised_prediction": int(un_pred),
            "unsupervised_anomaly_score": float(un_score),
            "final_prediction": int(final_prediction),
            "is_anomaly": final_prediction == 0,
            "xai_explanation": xai_explanation_summary,
            "raw_instance_data": instance_data.values[0].tolist()
        }

        return decision_log
