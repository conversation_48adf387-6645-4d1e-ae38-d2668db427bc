import sqlite3
from datetime import datetime, timedelta
import pandas as pd
import time
import os # NEW: Import the os module

class LogAnalyzerAgent:
    def __init__(self, db_path):
        self.db_path = db_path
        print(f"Log Analyzer Agent initialized, monitoring database: {self.db_path}")

    def analyze_logs(self, lookback_minutes=5):
        """
        Analyzes recent decisions in the database for aggregated patterns.
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            time_threshold = (datetime.now() - timedelta(minutes=lookback_minutes)).strftime('%Y-%m-%d %H:%M:%S')

            cursor.execute(f"""
                SELECT timestamp, source_ip, dest_ip, final_decision, action, log_message
                FROM decisions
                WHERE timestamp > ? AND (final_decision = 'Malicious Activity Detected' OR final_decision = 'Anomaly Detected')
                ORDER BY timestamp DESC
            """, (time_threshold,))
            
            recent_anomalies = cursor.fetchall()
            
            if not recent_anomalies:
                return

            print(f"\n--- Log Analyzer Report (Last {lookback_minutes} mins) ---")
            print(f"  Total Anomalies/Malicious Detections: {len(recent_anomalies)}")

            source_ips = [row[1] for row in recent_anomalies]
            dest_ips = [row[2] for row in recent_anomalies]
            
            source_ip_counts = pd.Series(source_ips).value_counts()
            frequent_sources = source_ip_counts[source_ip_counts > 1]

            if not frequent_sources.empty:
                print("  Potentially suspicious Source IPs (multiple anomalies):")
                for ip, count in frequent_sources.items():
                    print(f"    - {ip} ({count} occurrences)")

            dest_ip_counts = pd.Series(dest_ips).value_counts()
            frequent_destinations = dest_ip_counts[dest_ip_counts > 1]

            if not frequent_destinations.empty:
                print("  Potentially targeted Destination IPs (multiple anomalies):")
                for ip, count in frequent_destinations.items():
                    print(f"    - {ip} ({count} occurrences)")
            
            print("------------------------------------------")

        except sqlite3.Error as e:
            print(f"  Log Analyzer Error: {e}")
        finally:
            if conn:
                conn.close()

    def run_analysis_loop(self, interval_seconds=10, lookback_minutes=5):
        """Runs the analysis loop periodically."""
        print(f"Log Analyzer Agent starting analysis loop (every {interval_seconds}s)...")
        while True:
            self.analyze_logs(lookback_minutes)
            time.sleep(interval_seconds)

if __name__ == '__main__':
    test_db_path = "../logs/anms_log.db" 
    if not os.path.exists(test_db_path):
        print(f"Test database not found at {test_db_path}. Please run main.py first to generate data.")
    else:
        analyzer = LogAnalyzerAgent(test_db_path)
        analyzer.run_analysis_loop(interval_seconds=5, lookback_minutes=1)